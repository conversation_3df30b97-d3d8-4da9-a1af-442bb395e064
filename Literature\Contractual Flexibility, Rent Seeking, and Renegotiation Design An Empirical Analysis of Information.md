# Contractual Flexibility, Rent Seeking, and Renegotiation Design An Empirical Analysis of Information

**分析时间**: 2025-07-19 00:20:22
**原文件**: pdf_paper\Susarla - 2012 - Contractual Flexibility, Rent Seeking, and Renegotiation Design An Empirical Analysis of Informatio.pdf
**文件ID**: file-pzUk2qxAdjC6uvuyUJQC8UyU

---

## 📋 综合分析

# 一句话总结

这篇论文通过实证分析研究了信息技术外包合同中合同灵活性、寻租行为和再谈判设计之间的关系，发现灵活性条款、便利终止权和可重用知识权利与帕累托改进修正案显著相关。

## 论文概览

### 研究背景和动机

尽管大量研究认识到信息技术（IT）服务合同的不可避免的不完整性，但事后适应的作用尚未得到足够关注。传统的长期合同结构虽然旨在促进激励对齐和关系特定投资，但其僵化和不灵活性导致了多次合同取消。因此，实践中采用了再谈判（称为合同重组）作为应对僵化的手段。

### 主要研究问题

本文的主要研究问题是：事前决策权如何影响帕累托改进修正案的产生，以及如何在适应性和寻租之间解决权衡问题。

### 研究方法概述

本文通过收集141个IT外包合同的独特样本，采用适当的识别策略来解决潜在的内生性问题。提出了一个衡量指标——帕累托改进修正案，以评估通过风险平衡和学习增强外包价值的再谈判结果。

### 核心贡献和创新点

本文的贡献在于强调了再谈判设计在实现事后适应性方面的重要性，并提出了合同重新部署知识和知识产权的权利概念。本文还探讨了合同违约的替代解释，并通过多变量probit模型解决了潜在的同时性问题。

## 逐章详细分析

### 1. 引言（Introduction）

#### 主要内容

引言部分介绍了IT外包合同的背景和不完整性问题，指出了传统长期合同的僵化和不灵活性导致的合同取消问题。文章提出了再谈判作为一种应对措施，并强调了再谈判设计的重要性。

#### 关键概念和理论

- **不完全合同理论**：合同无法预见所有未来事件，因此需要再谈判来调整合同条款。
- **寻租行为**：合同双方在再谈判中寻求最大化自身利益，可能导致合同效率低下。

#### 实验设计或分析方法

- 收集了141个IT外包合同的样本，分析了合同条款和再谈判结果。
- 使用了文本分析方法来区分帕累托改进修正案和机会主义重新定价。

#### 主要发现和结论

- 灵活性条款、便利终止权和可重用知识权利与帕累托改进修正案显著相关。
- 再谈判设计可以有效减少适应性和寻租之间的权衡问题。

#### 与其他章节的逻辑关系

引言部分为后续章节奠定了基础，提出了研究问题和假设，并介绍了研究方法和数据来源。

### 2. 理论和假设（Theory and Hypotheses）

#### 主要内容

本章提出了三个主要假设，分别关于灵活性条款、便利终止权和可重用知识权利对帕累托改进修正案的影响。

#### 关键概念和理论

- **灵活性条款**：允许合同双方在事后调整合同条款的条款。
- **便利终止权**：赋予一方在无需理由的情况下终止合同的权利。
- **可重用知识权利**：允许供应商将合同中获得的知识和知识产权用于其他用途。

#### 实验设计或分析方法

- 使用了probit模型来检验假设。
- 控制了合同复杂性、特定投资和其他合同特征。

#### 主要发现和结论

- 灵活性条款、便利终止权和可重用知识权利显著增加了帕累托改进修正案的可能性。
- 这些条款通过减少事后适应性和寻租之间的权衡问题，提高了合同效率。

#### 与其他章节的逻辑关系

本章提出了研究假设，并为后续的实证分析提供了理论基础。

### 3. 数据和测量开发（Data and Measure Development）

#### 主要内容

本章描述了数据的来源和测量方法，包括合同条款、任务特征、合同特征、供应商和客户特征等。

#### 关键概念和理论

- **合同条款**：包括灵活性条款、便利终止权和可重用知识权利。
- **任务特征**：包括合同复杂性和特定投资。
- **合同特征**：包括固定价格、服务范围、输入监控和输出监控等。

#### 实验设计或分析方法

- 数据来自SEC的10-Q、10-K和8-K文件，以及其他公开数据库。
- 使用了详细的文本分析来编码合同条款和修正案。

#### 主要发现和结论

- 数据集包括了141个IT外包合同，涵盖了多种合同条款和特征。
- 文本分析方法有效地识别了帕累托改进修正案和机会主义重新定价。

#### 与其他章节的逻辑关系

本章提供了实证分析所需的数据和方法，为后续的统计分析奠定了基础。

### 4. 计量经济学方法（Econometric Approach）

#### 主要内容

本章介绍了计量经济学方法，包括基准估计、合同违约和帕累托改进修正案的联合估计、可行预见性和内生性问题的处理。

#### 关键概念和理论

- **基准估计**：使用probit模型比较帕累托改进修正案和未进行事后再谈判的合同。
- **联合估计**：使用多变量probit模型同时估计合同继续、终止和帕累托改进修正案的概率。
- **可行预见性和内生性问题**：通过工具变量法解决内生性问题。

#### 实验设计或分析方法

- 使用了多变量probit模型和工具变量法。
- 控制了合同复杂性、特定投资和其他合同特征。

#### 主要发现和结论

- 灵活性条款、便利终止权和可重用知识权利显著增加了帕累托改进修正案的可能性。
- 合同违约和帕累托改进修正案之间存在显著的联合效应。

#### 与其他章节的逻辑关系

本章提供了详细的计量经济学方法，验证了前文提出的假设，并解决了潜在的内生性问题。

### 5. 结果和讨论（Results and Discussion）

#### 主要内容

本章展示了实证结果，并讨论了其对文献和实践的意义。

#### 关键概念和理论

- **帕累托改进修正案**：通过风险平衡和学习增强外包价值的再谈判结果。
- **寻租行为**：合同双方在再谈判中寻求最大化自身利益，可能导致合同效率低下。

#### 实验设计或分析方法

- 使用了probit模型和多变量probit模型。
- 控制了合同复杂性、特定投资和其他合同特征。

#### 主要发现和结论

- 灵活性条款、便利终止权和可重用知识权利显著增加了帕累托改进修正案的可能性。
- 再谈判设计可以有效减少适应性和寻租之间的权衡问题。

#### 与其他章节的逻辑关系

本章总结了实证结果，并与前文的假设和理论进行了对比，验证了研究的有效性。

### 6. 结论和未来工作（Conclusions and Future Work）

#### 主要内容

本章总结了研究发现，并提出了未来研究的方向。

#### 关键概念和理论

- **再谈判设计**：通过灵活的合同条款和权利分配，减少适应性和寻租之间的权衡问题。
- **合同管理**：通过有效的合同管理和再谈判设计，提高外包合同的成功率。

#### 实验设计或分析方法

- 总结了实证研究的结果。
- 提出了未来研究的方向，包括关系契约和组织能力的研究。

#### 主要发现和结论

- 再谈判设计在实现事后适应性方面具有重要作用。
- 合同管理需要更加注重灵活性和适应性，以提高外包合同的成功率。

#### 与其他章节的逻辑关系

本章总结了全文的研究成果，并为未来的研究提供了方向。

## 总体评价

### 论文的优势和局限性

- **优势**：本文通过实证分析验证了合同灵活性、便利终止权和可重用知识权利对帕累托改进修正案的影响，提供了有力的理论和实证支持。研究方法严谨，数据处理细致，结论具有较高的可信度。
- **局限性**：本文的研究样本仅限于IT外包合同，可能存在行业特异性。此外，研究未考虑文化差异和其他外部因素对合同再谈判的影响。

### 对相关领域的影响和意义

本文的研究对合同管理和IT外包领域具有重要影响。通过强调再谈判设计和合同条款的重要性，本文为企业和研究者提供了新的视角和管理策略。研究结果有助于企业在签订合同时更好地设计合同条款，以提高合同成功率和降低交易成本。

### 未来研究方向的建议

未来的研究可以进一步探讨不同行业和文化背景下合同再谈判的设计和效果。此外，可以研究合同管理中的组织能力和关系契约对合同成功的影响，以提供更全面的管理建议。

---

## 📚 逐章节详细分析

以下是对论文各章节的深入分析，每个章节都经过了专门的AI分析：

### 第1章：Introduction

## 介绍

### 背景与动机

尽管大量研究表明信息技术（IT）服务合同不可避免地是不完整的，但事后适应的作用尚未得到足够的关注。传统的复杂IT外包交易通过长期合同构建，旨在促进激励对齐和促进持久的关系特定投资。然而，长期合同的僵化和不灵活性导致了一些合同在中途被取消，因为供应商未能满足客户对外包的期望。

### 研究目的

本文旨在探讨事前决策权在实现帕累托改进修正中的作用，并解决适应与寻租之间的权衡。具体来说，研究将考察灵活性条款、便利终止权和可重用知识权利等决策权如何影响外包的价值。

### 研究方法

本文采用独特的141个IT外包合同样本，通过危险平衡和学习来评估修订结果，以增强外包的价值。研究还考虑了合同违约的替代解释，并通过多变量probit模型来解决潜在的内生性问题。

### 理论贡献

本文的理论贡献在于强调了重新谈判设计在实现事后适应中的重要性。研究采用了不完全合同视角，认为重新谈判可以增强事后福利。此外，本文还概念化了重新部署专有技术和知识产权的权利，这是IT外包的一个特点，而其他地方分析的是耐用投资。

### 实践意义

本文的研究结果对实践者具有重要意义，特别是在管理外包合同中的适应性和寻租行为方面。通过强调灵活性条款和决策权的重要性，本文为企业在设计和管理外包合同时提供了实用的指导。

### 结构安排

本文的其余部分结构如下：第二节提出理论和假设；第三节描述数据和度量；第四节介绍实证方法；第五节讨论结果；第六节提供结论性意见。

---

### 第2章：Theory and Hypotheses

## 第2章：Theory and Hypotheses

### 2.1 IT外包中的租金寻求

在IT外包中，任务环境的特点是底层技术和输入（如硬件和网络基础设施）的不断变化（Gurbaxani 2007），以及业务需求、技术规格和应用架构的不断变化，以及用户期望的能力（例如，Barthelemy 2001）。任务环境的复杂性使得很难在合同中详细说明要涵盖的内容（例如，Banerjee和Duflo 2000），并概述可以评估供应商绩效的参数（例如，Whang 1992）。考虑一个支持索赔处理的新系统，供应商和客户可能只有在开发开始后才发现，如果整个应收账款流程发生变化，业务价值会更大。因此，各方可能会从一个不完善的系统规范开始签订合同，只有当外包计划进行时，各方才会纳入原始协议中未预见到的全部责任（例如，Huntley 2008）。

尽管IT外包合同不可避免地是不完整的，但实现适应性受到租金寻求的威胁。如前所述，我们在IT外包的背景下考虑了两种租金来源。首先，投资于了解客户组织和概念化流程重新设计是非常特定于客户的（Poppo和Zenger 1998），在关系内产生最大价值，符合特定投资的经典观点（Williamson 1985）。例如，正如以下来自其中一个合同的引文所示，合同条款可以定义供应商的责任：

...审查客户环境，目的是整合功能和机器，消除需求，并修改流程。

特定投资被描述为“有价值但脆弱”（Ghosh和John 1999，第134页），因为任何一方都可以利用双边锁定条件来从事租金寻求。其次，鉴于IT和组织资本之间的相互联系（Brynjolfsson和Hitt 2000），IT系统的另一个租金和价值创造来源是由于IT启用的管理实践（Miozzo和Grimshaw 2003）。因此，供应商在流程创新和流程改进方面的努力（例如，Linder等人2003）可以在替代环境中产生经济租金。因此，IT外包特有的另一种租金可能是能够重新部署在执行合同任务过程中产生的知识和人力资本（例如，Miozzo和Grimshaw 2005）。供应商和客户都可能寻求占有这种租金，这在重新谈判合同时导致两种不同的合同风险。重新谈判期间的让步提取（Masten 1988，Wathne和Heide 2000）以及由此产生的讨价还价成本导致适应不良（Williamson 1985）。当各方预期租金寻求时，可能导致投资不足，从而侵蚀合同的价值（Hart和Moore 1990）。

### 2.2 合同环境与帕累托改进修正案

图1展示了外包的时间线。在（i）选择的合同参数既影响（iv）重新谈判过程的结构，也影响（ii）各方的行动。基于不完全合同理论（例如，Hart和Moore 1990，Segal 1999），我们区分了阶段（i）的事前决策权和阶段（ii）的行动与阶段（iv）的事后行动。由于合同可能在（i）处开始时不完整，因此在（iv）处的修正是帕累托改进，当一方的福利得到改善而另一方没有变得更糟时（Guasch等人2008）。如前所述，我们考虑的两种事后合同风险是（ii）阶段的投资不足和（iv）阶段的适应不良，这两种风险都是由租金寻求引起的，当各方无法承诺不重新谈判时。

我们借鉴先前的文献阐述了修正案中的两种帕累托改进类型：风险平衡和学习。首先，交换伙伴需要在如何起草合同条款方面进行“本地和增量”学习（Mayer和Argyres 2004，第395页），例如纳入应急计划和未事先定义的责任（Mayer和Argyres 2004）。例如，一项修正案要求供应商对“（a）服务的最终用户和（b）最终用户的高级管理层”进行满意度调查，以提供供应商绩效的指标。另一项修正案规定了额外的责任，即“客户可以参与早期测试”，并扩大了原始协议，使完成的系统与客户组织的Web服务计划兼容。其次，“风险平衡”（Williamson 1985，第34页）是增加双方事后责任的过程，并给予他们更大的利益，以防止合同破裂，例如一项修正案中规定的“双方商定的里程碑时间表”。另一项修正案同样概述了关于预期绩效水平的双方商定的条件。在这两种情况下，通过相互调整平衡了每一方参与合同后讨价还价的动机（Williamson 1985）。这种风险平衡降低了事后调整的成本（Masten 2009），并确保重新谈判是公平的（Masten 1988）。

这两种类型的修正案都是帕累托改进，因为它们允许在披露有关合同环境的新信息时对条款进行事后修订（Roberts和Sufi 2009）。如附录（表A.1）中讨论的，帕累托改进的结果，如风险平衡和学习，允许各方纳入对新信息的相互调整，澄清外包的目标，并纳入外部环境的变化。

由于各方无法承诺不重新谈判（Aghion等人1994，Hart和Moore 1999，Segal 1999），实现合同灵活性的挑战在于平衡适应的需要与租金寻求引起的适应不良和投资不足的双重扭曲。鉴于IT服务的复杂性，第三方（如法院）可能难以区分被迫重新谈判和真正需要事后适应。这种区别很重要，否则合同可以纳入合同保障措施或基准以实现适应。

---

### 第3章：Data and Measure Development

## 第3章：数据与测量开发

### 数据来源

本文的数据来源于美国证券交易委员会（SEC）的10-Q、10-K和8-K文件，这些文件包含了公司必须公开披露的重大合同信息。尽管公司在披露哪些合同活动方面有很大的自由度，这限制了报告的外包活动量，但这些文件仍然提供了来自供应商和客户的全面合同数据源。SEC文件还提供了关于供应商和客户之间先前合同的详细信息以及合同修订的数据。

### 数据收集过程

数据收集过程如下：

1. **识别注册人**：从SEC的EDGAR数据库中识别出被归类为两位数SIC代码73的所有注册人，这表示合同是为计算机相关服务编写的。
2. **确定大客户**：基于专业咨询公司和行业期刊编制的外包公告新闻稿数据集，确定大客户的详细信息。
3. **样本组装**：在2006年至2007年期间，经过14个月的努力，最终组装了一个包含约3800份合同的完整样本。
4. **筛选过程**：通过筛选过程，最终样本缩小到161份合同，涉及232个客户和供应商。进一步的筛选确保了每个合同都有详细的描述和责任分配，并且包含了合同交付成果和各方责任的详细描述。

### 变量测量

#### 因变量

- **帕累托改进修正案**：这是一个二元变量，基于对修正案的详细文本分析，评估双方是否表现出学习（例如，Mayer和Argyres 2004）和风险均衡（Masten 1988, 2009）。
- **终止**：客户（通常是供应商）在其新闻稿和投资者简报中披露提前终止的合同，这些数据用于编码合同取消。

#### 自变量

- **灵活性条款**：这是一个二元变量，取决于合同中是否存在更新服务条款和允许价格变动的两个条款。
- **重新部署权**：这个变量通过检查供应商在履行合同任务过程中产生的知识和知识产权的剩余控制权来评估。
- **便利终止权**：这个变量在合同中指定客户有权在没有原因的情况下提前终止协议时编码为1。

#### 任务特征

- **复杂性**：复杂性是指事先难以预见和事后难以执行的意外情况。
- **特定投资**：特定投资是指“为满足与交易伙伴打交道的要求而进行的定制化投资”。

#### 合同特征

- **固定价格**：合同规定了服务的明确支付时间表。
- **服务范围**：供应商提供的服务按14个子服务的分类进行分类。
- **输入监控**：如果合同中包含详细的工作执行描述，则编码为1。
- **输出监控**：客户可以监控结果的三个变量：绩效里程碑、审计权和供应商提供的服务质量的服务水平协议。

#### 供应商和客户特征

- **供应商声誉**：如果供应商被列入《财富》1000强科技公司、《信息周刊》500强科技公司或《商业2.0》100家增长最快的科技公司之一，则编码为1。
- **主导客户**：如果客户公司占供应商年收入的10%或以上，则编码为1。
- **客户规模和供应商规模**：以员工数的自然对数衡量。
- **上市公司**：通过一个变量衡量供应商是否有进入资本市场的渠道。
- **合同价值**：从合同中获取并验证，以百万美元的对数衡量。
- **不可抗力**：如果合同条款规定在事后出现代价高昂的意外情况时可以轻松退出，则编码为1。

### 结论

本章详细描述了数据的来源、收集过程和变量的测量方法。通过对141份IT外包合同的详细分析，本文旨在探讨合同设计中的决策权如何影响帕累托改进修正案的可能性。研究结果表明，灵活性条款、便利终止权和重新部署权与帕累托改进修正案显著相关，支持了不完全合同理论的观点，即合同设计应考虑事后的适应性和灵活性。

---

### 第4章：Econometric Approach

## 第4章：计量经济学方法

### 4.1 描述性概述

本文的研究样本涵盖了1998年至2003年期间签订的合同，这些合同在结构上表现出显著的异质性，包括供应商责任、治理条款、管理评估流程和组织间沟通程序等方面。客户通常会发起合同的修订和终止。数据中观察到的修订和终止类型如下表所示。

表2显示了合同修订的描述性统计信息。根据图1所示的时间线，客户和供应商可以选择继续执行初始合同而不进行重新谈判，或者终止合同，或者通过修订合同来实现租金转移或帕累托改进。未修订或未终止的合同则按照原始合同条款执行。

表3展示了基于合同特征的帕累托改进修订和终止的交叉制表。附录中的表A.1提供了在年度报告和供应商及客户的文件中披露的修订和终止的详细示例。

初步证据表明，关于重新谈判设计的理论论点是成立的。平均而言，服务复杂性越高、合同期限超过三年、合同价值每增加1000万美元以及当客户是主导客户时，帕累托改进修订的可能性越大。灵活性条款使帕累托改进修订的可能性增加了25%，而便利终止权使可能性增加了18%，可再部署权使可能性增加了21%。

### 4.2 基准估计

首先进行了一个probit模型，将帕累托改进修订的发生率与合同在没有事后重新谈判的情况下执行的情况进行比较。在计量经济模型中，i表示客户，j表示供应商；Aij = 1表示客户i和供应商j之间的合同发生了帕累托改进修订，Aij = 0表示双方继续执行原始合同而不进行重新谈判；θij表示事前界定的决策权；Xij表示合同任务的特征；Cij表示一系列合同条款；Vj是一个向量，表示供应商特定特征；Fij是一个向量，表示客户特定特征。我们控制了提高双方对交换需求理解的变量，如监管和绩效条款，这些条款可能降低潜在的机会主义行为，以及先前的关系和合同协调机制，这些因素可能促进适应性调整并最小化事后摩擦。

由于样本中有一些供应商和客户重复出现，我们还采用了多向聚类稳健（MWCR）probit规格来处理误差项可能在客户和供应商之间相关的可能性。我们采用了一种统计方法，该方法建立在先前工作的基础上，通过推广独立异方差误差来提供无偏估计。

### 4.3 合同违约与帕累托改进修订

鉴于图1中概述的合同时间线，供应商和客户在签订合同后面临几个选择：（i）他们可以选择继续执行初始合同而不进行重新谈判，（ii）他们可以终止合同，或者（iii）他们可以通过修订合同来进行修订。由于我们在修订中没有观察到显著的租金寻求情况，因此我们将这些情况从分析中排除。

为了应对供应商和客户可能面临多重选择而非单一选择的可能性，我们进行了多元probit估计。多元probit具有类似于似不相关回归（SUR）模型的结构，其中不同的结果可能依赖于一组共同的解释变量。我们使用了由Cappellari和Jenkins（2006）开发的Stata模块进行了模拟最大似然估计。

### 4.4 可行预见性和灵活性条款的内生性

实证估计的一个重要问题是合同中灵活性条款与观察到的帕累托改进修订之间的潜在内生性。根据可行预见性的论点，即使合同方缺乏确切的知识来解决交换中可能出现的情况，他们仍然可以预见合同风险并设计合同以允许日后顺利适应。允许顺利重新谈判的合同条款的存在可能是内生于观察到的重新谈判的。

### 4.5 鲁棒性检验

实证估计的一个可能的担忧是逆向选择，当不确定性可能导致各方机会主义地利用合同形式来触发合同修订的需要时（这增加了租金寻求的收益）。遵循测试逆向选择的文献，我们进行了合同选择和观察到的修订的双变量probit估计。如果采用时间和材料合同的价格结构，逆向选择可能导致合同选择和帕累托改进修订的概率相关，从而导致两个误差项之间的正相关。

另一个可能的估计偏差来源是质量上的逆向选择，如果供应商故意寻找监控条款不那么严格的合同。相反，客户可能会对供应商施加严格的质量和性能规范，以迫使后者在重新谈判中放弃租金。

我们对帕累托改进修订和服务水平协议进行了双变量probit估计，后者作为合同中严格质量规范的代理。估计未能拒绝无相关的零假设。结果支持不完全合同理论的观点，即重新谈判能够实现超出吸收成本超支的事后福利增强。结果可根据要求提供。

另一个需要考虑的实证估计问题是便利终止权和可再部署权是否可能在合同中同时出现。我们遵循Novak和Stern（2009）的方法，检查了这两个合同条款之间的条件相关性。我们首先使用合同对占用的限制作为工具进行了可再部署权的probit估计，然后在进行便利终止权的probit估计时包括了可再部署权作为解释变量。残差之间的相关性使我们能够估计互补性。估计未能拒绝残差之间无相关的零假设。因此，我们没有发现合同条款共同设计的证据。结果可根据要求提供。

---

### 第5章：Results and Discussion

## 第5章：结果与讨论

### 决策权与帕累托改进修正案

图3展示了初始合同条款的不完整性增加时，帕累托改进修正案的可能性也随之增加。表4的第三列强调了更广泛的服务范围、长期合同和复杂性增加了帕累托改进修正案的可能性，而特定投资在资产关系外价值有限时则减少了这种可能性。先前的文献认为，详细的合同保障措施可以防止事后征用。然而，防止租金征用的合同保障措施实际上可能导致其他类型的租金寻求行为（例如，Gibbon 2005）。事实上，表4第二列的估计表明，只有服务水平协议和里程碑两个合同条款增加了帕累托改进修正案的可能性，这表明合同意外事件可能对防止暴露风险的能力有限。

表4第四列指出，使适应成为可能的灵活性条款与帕累托改进修正案显著相关，支持假设1。通过更好地阐述合同意外事件并纳入外包中学到的经验教训，交换伙伴的后交易福利可以得到改善，这与Williamson（1985）的风险平衡论点和Mayer和Argyres（2004）的学习合同论点一致。帕累托改进修正案意味着供应商和客户可以通过丰富的信息交流和谐地解决事后揭示的意外情况。由于复杂的IT服务涉及大量的业务流程重组，事先定义供应商的责任可能很困难。灵活性条款是设计适应性采购安排的重要方面，交换伙伴可以在其中纳入不断变化的业务优先事项和监管要求。

表4第四列还指出，客户单方面终止便利权显著增加了帕累托改进修正案的可能性，验证了假设2。鉴于供应商进行了特定的资产投资，合同中赋予客户的单方面决策权通过阻止供应商的欠投资来降低讨价还价能力。终止便利权与帕累托改进修正案之间的关联也表明，这些权利阻止了客户进行战略性终止，通过双边锁定来约束双方的过度租金寻求。当供应商有权专利或授权在执行合同任务中获得的知识诀窍时，帕累托改进修正案的可能性更大，验证了假设3。可重新部署性作为一种外部选择，通过加强投资激励和减少谈判中的摩擦，增强了供应商重新部署流程知识和技术的能力。当供应商的投资将在未来提供价值时，他们不太可能从事租金寻求行为。客户相应地从终止合同中获得的收益较少。图4展示了决策权的作用。y轴表示帕累托改进修正案的概率，x轴表示合同的复杂性。

我们发现，即使在包括供应商和客户的完整控制集并控制供应商和客户的聚类后，决策权仍然很重要，如表4第五列所示，这表明可能不是可征用租金的大小，而是决策权的结构使得帕累托改进修正案成为可能。表4中的probit估计与表5中的多元probit估计一致。可重新部署性和终止便利性的作用符合强调所有权的公司理论，即重要的不是谁拥有剩余控制权，而是这些控制权是否明确界定。

表6提供了帕累托改进修正案和终止的同时性的证据，支持合同违约理论和不完全合同理论的观点。一个IT外包项目可能始于对需要完成的工作的不完美理解，而修正案可以帮助编纂更清晰的目标集。通过在前期不对合同关系做出过多承诺，各方有足够的灵活性根据进展修改合同或退出合同。

我们发现了设计具有灵活性条款的合同的内生性证据。当各方预期合同需要重新谈判时，灵活性条款实际上降低了帕累托改进修正案的可能性，如表7所示。Fehr等人（2011）提供的证据表明，正式合同作为交易关系的参考点。灵活性条款可能提供了一个非正式的框架，以实现事后的相互调整，从而避免了通过合同修正案实现的正式合同重新谈判的需要。可行的预见意味着各方可以预见交易风险并制定合同以减轻这些风险（Williamson 2005）。因此，灵活性条款可能提供适应不断展开的意外情况的灵活性，而不会伴随正式合同重新谈判的欠投资或适应不良的风险。内置灵活性的设计表明，各方可以显式设计合同，考虑到编写合同成本和重新谈判合同成本之间的权衡（例如，Schwartz和Watson 2004）。

另一个我们需要考虑的解释是，关系作为允许各方相互理解和起草能够实现适应性的合同的替代机制（例如，Banerjee和Duflo 2000）。尽管结果表明先前的关系确实增加了帕累托改进修正案的可能性，但在控制先前的关系后，三个决策权的系数仍然高度显著。由于复杂性的来源是业务转型，先前的合同经验可能有助于平滑公司间的互动，但不能完全有效地实现适应性。

### 对文献和实践的影响

尽管IT外包的重要性日益增加，但长期外包安排的僵化和不灵活性让寻求从外包中获得可持续价值的公司感到沮丧。关注合同内的后合同治理以及企业如何在合同内管理适应性问题，是研究人员和实践者共同面临的一个重要问题。本文朝着这个方向迈出了第一步，强调了重新谈判设计在实现外包灵活性方面的作用。帕累托改进修正案重新定义了外包的结构，纳入了原始合同中未包含的规定，如执行和监控规定，以及增强交换价值的管理控制系统。鉴于事前验证的困难和难以预见所有意外情况（例如，Segal 1999），事前决策权导致了帕累托改进修正案。

我们考虑了构建事后适应条款的灵活性条款、赋予客户单方面谈判权的便利终止权以及赋予供应商剩余租金权的可重新部署权的作用。我们的论文表明，有意设计的事前决策权解决了租金寻求导致的适应不良和欠投资威胁。鉴于技术变革的速度以及商业和监管环境的变化，重新谈判设计对于实现灵活的采购安排至关重要。我们朝着这个方向迈出了第一步。我们强调了可以实现帕累托改进适应同时最小化租金寻求来源的三类决策权的作用。鉴于缔约方的意图在合同过程中会发生变化，我们强调了为灵活性签订合同的重要性。这里强调的适应的帕累托改进作用与将事后重新谈判视为低效治理选择的文献（Anderson和Dekker 2005）以及认为详细的服务范围条款和全面的绩效衡量标准是IT外包成功的关键的先前工作形成了对比（例如，Lacity和Willcocks 1998）。

当客户和供应商参与事后讨价还价和谈判时，可能会给客户带来多种适应不良成本，如项目进度中断、生产力损失以及业务中断的风险。供应商的欠投资也降低了外包的收益，导致客户不满。因此，当各方不能承诺不重新谈判时，预期中的争议性谈判会导致事前低效的投资，从而降低交换的收益（例如，Hart和Moore 1999），这降低了编写详细合同的价值。当各方对未来知之甚少时，事前起草详细的合同条款可能在事后是低效的（例如，Segal 1999）。Gibbons（2005）认为，或有合同实际上可能会加剧机会主义。执行合同的难度和对义务精确描述的限制可能导致合同不完整（例如，Susarla等人2010）。随着技术变革的速度和需求的演变，带有严厉处罚和服务规格的合同可能会变得无关紧要。规划终止的重要性在于，治理结构需要纳入过渡规划条款以及平稳退出的条款。

虽然文献已经从软件开发指标的维度考察了供应商的能力（Harter等人2000年），但未来的工作可以考察供应商组织（以及客户组织中的平行过程）中促进帕累托改进适应的过程。例如，供应商声誉已被确定为IT外包中事后谈判和执行的关键决定因素（例如，Banerjee和Duflo 2000年）。在协作合同关系中的各方也可以发展适应性能力（Gulati等人2005年），从而能够更好地响应事后情况。供应商应发展在企业间沟通和治理结构方面的专长，以处理事后适应，而客户则需要发展建立协作过程的复杂性。此外，客户和供应商应在通过事后适应实现协作价值方面发展互补能力（例如，Levina和Ross 2003年）。

从外包计划中获得的可重新部署的宝贵技能和知识诀窍的权利，出于两个原因很重要。首先，从一个外包计划到另一个外包计划重新部署知识和学习的能力可能是供应商的竞争优势来源（例如，Mayer 2006年）。事实上，外包的一个动机可能是企业能够获得供应商的能力，而对决策权的审慎分配保护了供应商的市场地位。可重新部署性权利还可以确保供应商通过流程改进实现的效率提升能够传递给其他买家。其次，鉴于人力资本在IT服务行业中的重要性，供应商的知识和学习作用可能非常复杂。即使客户对开发的软件拥有完整的剩余权利，供应商也可能从为特定行业的客户提供服务的知识溢出中受益。同样，供应商可能会获得复杂的流程转型或行业知识，这些知识可以在其他地方部署。Mayer（2006年）认为，当供应商预期知识可以被重新使用时，他们会增加任务细节的程度。然而，危险在于供应商可能会将资源转移到那些可能产生知识溢出的任务上。此外，为了使重新谈判的结构有所不同，各方应该拥有影响重新谈判价值的外部选择（MacLeod和Malcomson 1993年）。尽管由于知识溢出可能会加强供应商的市场地位，但可重新部署性作为一种合同规定的外部选择，可以实现事后有效的适应。换句话说，重要的不是通过知识溢出或可专利知识诀窍创造的价值类型，而是讨价还价权力的结构。

### 局限性

最近的法律文献（例如，Eggleston等人2000年，Katz 2005年）和经济文献（Schwartz和Watson 2004年）表明，在存在合同成本的情况下，重新谈判的需要使得编写更简单的合同是最优的。这对估计提出了两个问题。首先，事后重新谈判的结构可能受到非合同执行机制的影响。我们不能完全排除帕累托改进修正案是否来自决策权以外的替代机制。其次，我们也没有研究是否存在棘轮效应，即客户在重新谈判过程中根据过去的表现提高标准。换句话说，企业过去对合同的熟悉程度可能只会增加负面结果，如欠投资和适应不良。重新谈判设计可能会受到供应商积累的隐性知识的影响，这些知识可以惠及同一行业的其他客户。我们没有探讨更长期的知识溢出是否通过可重新部署性产生。也有可能可重新部署性权利会降低供应商挪用客户知识产权或有价值的无形资产（如流程知识）的动机。将专利引用数据库与合同的纵向数据结合起来，可以获得更多关于可重新部署性、挪用和知识生成的作用的见解。

虽然我们将终止视为一种替代解释，但我们没有区分不同的违约结果。未来的工作可以考虑违约条款，如预期损害赔偿或特定履行（例如，Shavell 1984年），以及这些规定是否与事前决策权一起发挥作用。另一种未探讨的合同风险是，供应商可能会故意增加专有技术或流程的范围，从而增加客户的转换成本，然后机会主义地提高服务费用，利用客户对供应商的依赖。

## 结论与未来工作

企业越来越多地出于战略目标而非仅仅为了节省成本而进行外包（例如，Linder 2004年）。矛盾的是，外包的增长伴随着对IT外包计划的不满情绪的增加。一系列合同重组和取消使得重新谈判设计对从业人员和研究人员来说都是一个重要问题，然而相对较少的学术研究涉及这一问题。IT外包成功或失败率的巨大差异表明，企业在外包合同中管理适应性的方式是研究人员和从业人员共同面临的一个重要问题。关注事后结果可以深入了解管理客户和供应商之间互动的重要因素，并最终更好地理解导致IT外包可持续价值的因素。

本文的一个贡献是调查重新谈判设计在促进IT外包灵活性方面的作用。事后重组使供应商和客户能够从他们的经验中学习，并实现风险平衡。本文的另一个贡献是强调IT背景下投资的性质及其对重新谈判设计的影响。一种补充方法是研究关系合同的作用，这种合同能够实现联合治理和解决程序。未来的工作可以研究组织间的关系资本如何导致发展促进相互适应和合同灵活性的能力。

## 致谢

作者感谢Lan Shi和Ramanath Subramanyam的有益讨论。她还感谢INFORMS 2009会议的参与者，Anitesh Barua, Gordon Gao, Vijay Gurbaxani, Ted Klastorin, Natalia Levina, Tridas Mukhopadhyay, Barrie Nault, Gautam Ray和V. Sambamurthy对本文早期草稿的反馈；部门编辑Sandra Slaughter和匿名评审团队提供的评论和建议。Lew McMurran提供了联系华盛顿技术行业协会成员的帮助。Christina Tapia, Mike Hansen, David Harman和Taliesin Reimers提供了研究协助。所有错误和遗漏均为作者本人负责。作者目前的隶属关系是密歇根州立大学Eli Broad商学院，东兰辛，密歇根州48824。

---

### 第6章：Conclusions and Future Work

## 第6章：结论与未来工作

### 1. 结论概述

本文探讨了信息技术外包合同中的再谈判设计，特别是灵活性条款、便利终止权和可重新部署权对帕累托改进修正的影响。研究发现，这些决策权显著提高了帕累托改进修正的可能性，减少了因租金寻求导致的适应不良和投资不足。

### 2. 主要贡献

- **再谈判设计的重要性**：本文强调了再谈判设计在实现外包适应性方面的重要性。通过灵活性条款、便利终止权和可重新部署权的设计，可以有效减少适应不良和投资不足的问题。
  
- **决策权的作用**：研究表明，灵活性条款、便利终止权和可重新部署权显著提高了帕累托改进修正的可能性。这些条款通过减少租金寻求行为，促进了双方的互利合作。

- **实证研究的贡献**：本文通过收集和分析大量IT外包合同的实证数据，验证了理论假设的有效性。研究方法严谨，数据来源可靠，为相关领域的研究提供了有力的支持。

### 3. 对文献和实践的意义

- **文献贡献**：本文丰富了不完全合同理论和再谈判设计的实证研究。通过引入帕累托改进修正的概念，扩展了对合同灵活性和租金寻求行为的理解。

- **实践意义**：本文的研究结果对企业和外包服务提供商具有重要的指导意义。建议企业在签订外包合同时，应充分考虑灵活性条款、便利终止权和可重新部署权的设计，以提高合同的适应性和效率。

### 4. 未来研究方向

- **关系契约和组织能力**：未来的研究可以进一步探讨关系契约在促进联合治理和解决程序中的作用。通过研究组织间的关系资本，可以更好地理解如何发展促进相互适应和灵活性的能力。

- **知识转移和溢出效应**：未来的研究可以进一步探讨供应商在多个外包项目中转移和利用知识的能力。这包括研究知识溢出对供应商市场地位的影响，以及如何通过合同条款保护和管理这些溢出效应。

- **违约条款和期望损害**：未来的研究可以考虑不同的违约条款（如期望损害赔偿或特定履行）及其与事先决策权的相互作用。这将有助于更全面地理解合同设计和执行中的风险管理和利益分配。

- **技术变革和市场需求**：随着技术和市场需求的快速变化，未来的研究可以探讨长期知识溢出和技术能力的动态变化。这将有助于企业更好地应对未来的不确定性，并制定更有效的合同策略。

### 5. 研究局限性与挑战

- **非合同机制的影响**：本文未能完全排除非合同机制对事后再谈判的影响。未来的研究可以通过结合其他数据源和方法，进一步探讨这些机制的作用。

- **质量选择的偏差**：本文未探讨因质量选择导致的偏差问题。未来的研究可以通过更细致的数据分析和控制变量，进一步验证和完善相关假设。

- **可重新部署权的复杂性**：本文对可重新部署权的研究较为初步，未来的研究可以进一步探讨其在不同情境下的具体应用和效果。

### 6. 总结

本文通过实证研究验证了再谈判设计在IT外包合同中的重要性，特别是灵活性条款、便利终止权和可重新部署权对帕累托改进修正的积极影响。研究结果为企业在外包合同管理中提供了重要的理论依据和实践指导。未来的研究可以在现有基础上进一步拓展和深化，以更好地应对不断变化的商业环境和技术挑战。

---

## 📊 分析统计

- **综合分析**: ✅ 已完成
- **章节分析**: 6 个章节
- **总分析数**: 7 项
- **分析引擎**: 腾讯混元API
- **分析模式**: 多提示词深度分析

---

*本分析由腾讯混元API自动生成，采用多轮对话模式进行深度学术分析*
