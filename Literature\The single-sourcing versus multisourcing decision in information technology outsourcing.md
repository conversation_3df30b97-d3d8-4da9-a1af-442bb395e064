# The single-sourcing versus multisourcing decision in information technology outsourcing

**分析时间**: 2025-07-18 22:35:42
**原文件**: pdf_paper\Handley 等 - 2022 - The single‐sourcing versus multisourcing decision in information technology outsourcing.pdf
**文件ID**: file-akArAEbToOyyJi7cKCyMAAwD

---

## 📋 综合分析

## 1. 一句话总结

这篇论文探讨了信息技术外包中单一来源与多来源决策的影响因素，发现云计算服务更倾向于单一来源，而自动化服务的多来源决策更具情境性，客户经验丰富度对多来源决策有正向影响，而供应商经验和离岸位置则对多来源决策有负向影响。

## 2. 论文概览

### 研究背景和动机

近年来，信息技术（IT）服务的外包策略从单一来源转向多来源的趋势日益明显。尽管单一来源外包在早期占据主导地位，但多来源外包因其灵活性和风险分散优势逐渐受到青睐。然而，关于促使这一趋势的因素的研究仍然有限。

### 主要研究问题

本文旨在探讨哪些因素影响客户组织在选择单一来源或多来源IT服务外包策略时的决策。具体而言，研究了新兴技术（如云计算和自动化服务）、供应商位置（离岸或国内）以及经验学习（客户和供应商经验）对多来源决策的影响。

### 研究方法概述

本文采用基于知识视角的理论框架，利用2007年至2017年间全球签署的多份多年、数百万美元的IT服务合同数据库进行分析。通过逻辑回归模型，检验了新兴技术、供应商位置和经验学习对多来源决策的影响。

### 核心贡献和创新点

本文的主要贡献在于填补了现有文献中对IT服务外包多来源决策因素研究的空白。通过实证分析，揭示了云计算服务和自动化服务在不同情境下的多来源决策差异，并强调了客户和供应商经验在多来源决策中的重要性。此外，本文还探讨了供应商位置对多来源决策的影响，提供了新的见解。

## 3. 逐章详细分析

### 1. 引言（Introduction）

#### 主要内容

引言部分介绍了IT服务外包策略从单一来源向多来源转变的背景，并提出了研究问题：什么因素影响客户组织选择单一来源或多来源IT服务外包策略？通过引用行业案例和研究背景，说明了多来源外包的优势和挑战。

#### 关键概念和理论

- 单一来源外包（Single-sourcing）
- 多来源外包（Multi-sourcing）
- 新兴技术（Emerging technologies）
- 经验学习（Experiential learning）

#### 实验设计或分析方法

引言部分未涉及具体的实验设计或分析方法，主要是提出研究问题和背景。

#### 主要发现和结论

引言部分总结了多来源外包的趋势及其优势和挑战，提出了研究问题的重要性。

#### 与其他章节的逻辑关系

引言部分为后续章节的研究背景和动机提供了基础，引出了本文的核心研究问题。

### 2. 理论背景（Theoretical Background）

#### 主要内容

理论背景部分回顾了现有的关于单一来源与多来源决策的文献，指出了现有研究的不足，并提出了本文的研究框架。讨论了分布式知识工作和协调治理障碍对多来源决策的影响。

#### 关键概念和理论

- 分布式知识工作（Distributed knowledge work）
- 协调治理障碍（Coordination and governance obstacles）
- 技术能力（Technological capabilities）

#### 实验设计或分析方法

理论背景部分未涉及具体的实验设计或分析方法，主要是文献综述和理论框架的构建。

#### 主要发现和结论

理论背景部分总结了现有文献的不足，提出了本文的研究框架，并强调了新兴技术和经验学习在多来源决策中的重要性。

#### 与其他章节的逻辑关系

理论背景部分为后续章节的研究假设和数据分析提供了理论基础。

### 3. 研究假设（Hypotheses）

#### 主要内容

研究假设部分提出了五个假设，分别探讨了新兴技术、供应商位置和经验学习对多来源决策的影响。具体假设包括：云计算服务和自动化服务对多来源决策的负向影响，离岸供应商对多来源决策的负向影响，客户经验对多来源决策的正向影响，以及供应商经验对多来源决策的负向影响。

#### 关键概念和理论

- 新兴技术（Emerging technologies）
- 离岸供应商（Offshoring）
- 客户经验（Client experience）
- 供应商经验（Vendor experience）

#### 实验设计或分析方法

研究假设部分未涉及具体的实验设计或分析方法，主要是提出研究假设。

#### 主要发现和结论

研究假设部分提出了五个假设，为后续章节的数据分析和结果验证提供了理论依据。

#### 与其他章节的逻辑关系

研究假设部分为后续章节的数据分析和结果验证提供了理论基础。

### 4. 数据和测量（Data and Measures）

#### 主要内容

数据和测量部分介绍了研究所使用的数据来源和变量测量方法。数据来自国际数据公司（IDC）的服务合同数据库，涵盖了2007年至2017年间全球签署的IT服务合同。变量包括因变量（多来源决策）、自变量（新兴技术、供应商位置、经验学习）和控制变量（合同规模、持续时间、定价类型等）。

#### 关键概念和理论

- 多来源决策（Multi-sourcing decision）
- 新兴技术（Emerging technologies）
- 供应商位置（Supplier location）
- 经验学习（Experiential learning）

#### 实验设计或分析方法

- 数据来源：IDC服务合同数据库
- 变量测量：逻辑回归模型

#### 主要发现和结论

数据和测量部分介绍了数据来源和变量测量方法，为后续章节的数据分析提供了基础。

#### 与其他章节的逻辑关系

数据和测量部分为后续章节的数据分析和结果验证提供了数据基础。

### 5. 主要分析和结果（Main Analysis and Results）

#### 主要内容

主要分析和结果部分详细描述了数据分析方法和结果。使用了多路聚类鲁棒估计方法（MWRCE）来处理数据的非独立性问题。结果表明，云计算服务对多来源决策有显著的负向影响，自动化服务对多来源决策的影响不显著，离岸供应商对多来源决策有显著的负向影响，客户经验对多来源决策有显著的正向影响，供应商经验对多来源决策有显著的负向影响。

#### 关键概念和理论

- 多路聚类鲁棒估计方法（MWRCE）
- 逻辑回归模型（Logistic regression model）

#### 实验设计或分析方法

- 数据分析方法：多路聚类鲁棒估计方法
- 模型构建：逻辑回归模型

#### 主要发现和结论

主要分析和结果部分揭示了云计算服务、离岸供应商、客户经验和供应商经验对多来源决策的影响，验证了研究假设。

#### 与其他章节的逻辑关系

主要分析和结果部分为后续章节的结果讨论和结论提供了实证依据。

### 6. 稳健性检验（Robustness Checks）

#### 主要内容

稳健性检验部分通过多种方法验证了主要结果的稳健性。包括：将多来源合同合并为单一观察值、细分云计算和自动化服务的类别、使用不同的标准误聚类方法和数据过滤方法。结果表明，主要结果在不同检验方法下均保持一致。

#### 关键概念和理论

- 稳健性检验（Robustness checks）
- 标准误聚类方法（Standard error clustering methods）

#### 实验设计或分析方法

- 数据处理方法：合并多来源合同、细分服务类别
- 模型构建：逻辑回归模型

#### 主要发现和结论

稳健性检验部分验证了主要结果的稳健性，确保了研究结论的可靠性。

#### 与其他章节的逻辑关系

稳健性检验部分为后续章节的结果讨论和结论提供了进一步的实证支持。

### 7. 内生性检验（Endogeneity）

#### 主要内容

内生性检验部分探讨了潜在的内生性问题，即客户对新技术的采用可能是非随机选择，可能与未观察到的因素相关。通过工具变量法（IV）解决了内生性问题，使用了供应商的云计算和自动化服务合同数量作为工具变量。

#### 关键概念和理论

- 内生性问题（Endogeneity）
- 工具变量法（Instrumental variable method）

#### 实验设计或分析方法

- 工具变量：供应商的云计算和自动化服务合同数量
- 模型构建：两阶段最小二乘法（2SLS）

#### 主要发现和结论

内生性检验部分验证了主要结果的稳健性，确保了研究结论的可靠性。

#### 与其他章节的逻辑关系

内生性检验部分为后续章节的结果讨论和结论提供了进一步的实证支持。

### 8. 事后分析（Post Hoc Analyses）

#### 主要内容

事后分析部分进一步探讨了供应商和客户位置对多来源决策的调节效应。结果表明，离岸供应商对供应商经验与多来源决策的关系有显著的调节效应，客户所在国家的经济水平对自动化服务与多来源决策的关系有显著的调节效应。

#### 关键概念和理论

- 调节效应（Moderating effect）
- 经济发展水平（Economic development level）

#### 实验设计或分析方法

- 调节效应分析：交互项分析
- 模型构建：逻辑回归模型

#### 主要发现和结论

事后分析部分揭示了供应商和客户位置对多来源决策的调节效应，提供了更细致的洞察。

#### 与其他章节的逻辑关系

事后分析部分为后续章节的结果讨论和结论提供了更深入的分析。

### 9. 讨论和结论（Discussion and Conclusions）

#### 主要内容

讨论和结论部分总结了研究发现，探讨了研究的理论和实践意义，并提出了未来研究方向。研究表明，新兴技术、供应商位置和经验学习对多来源决策有显著影响。研究结果对供应商和客户的管理团队具有重要的启示。

#### 关键概念和理论

- 新兴技术（Emerging technologies）
- 供应商位置（Supplier location）
- 经验学习（Experiential learning）

#### 实验设计或分析方法

讨论和结论部分未涉及具体的实验设计或分析方法，主要是对研究结果的总结和讨论。

#### 主要发现和结论

讨论和结论部分总结了研究发现，强调了新兴技术、供应商位置和经验学习在多来源决策中的重要性，并提出了对供应商和客户的启示。

#### 与其他章节的逻辑关系

讨论和结论部分总结了全文的研究发现，回应了引言部分提出的研究问题。

## 4. 总体评价

### 优势和局限性

#### 优势

- **理论贡献**：填补了现有文献中对IT服务外包多来源决策因素研究的空白，提供了新的理论视角。
- **实证研究**：利用大规模的合同数据库进行实证分析，增强了研究结果的可靠性和说服力。
- **多角度分析**：综合考虑了新兴技术、供应商位置和经验学习等多个因素，提供了全面的分析视角。

#### 局限性

- **数据限制**：研究数据主要来自IDC数据库，可能存在数据选择偏差。
- **情境局限**：研究结果可能在不同行业和文化背景下有所不同，需进一步验证。
- **未来研究方向**：建议未来研究可以进一步探讨不同新兴技术对多来源决策的具体影响机制。

### 对相关领域的影响和意义

本文的研究成果对IT服务外包领域的理论和实践具有重要意义。理论上，提供了新的视角和实证证据，丰富了现有的外包决策理论。实践上，为企业在进行IT服务外包决策时提供了有价值的参考，帮助企业更好地选择合适的供应商策略。

### 未来研究方向的建议

- **新兴技术的具体影响**：进一步探讨不同新兴技术（如人工智能、区块链等）对多来源决策的具体影响机制。
- **跨文化和跨行业研究**：研究不同文化和行业背景下多来源决策的差异，验证本文结论的普适性。
- **动态视角**：采用动态视角，研究企业在不同发展阶段的多来源决策变化及其影响因素。

---

## 📚 逐章节详细分析

以下是对论文各章节的深入分析，每个章节都经过了专门的AI分析：

### 第1章：Introduction

## 第1章：Introduction

### 背景与研究问题

近年来，信息技术（IT）服务的外包策略发生了显著变化。早期的IT外包主要以单一供应商模式为主，即大型合同授予单一供应商。然而，近年来，多供应商模式逐渐兴起，客户通过多个供应商来管理其IT需求的各个部分。这种转变反映了企业在IT服务采购策略上的多样化和复杂化。

本文旨在探讨影响企业选择单一供应商或多供应商模式的IT服务外包决策的因素。尽管多供应商模式日益流行，但选择单一供应商还是多供应商并非显而易见。单一供应商模式的优势在于能够促进客户特定的投资、减少协调和控制跨组织任务的挑战。而多供应商模式则提供了“最佳品种”策略、降低了机会主义风险、促进了供应商之间的竞争，并减少了技术平台的锁定风险。

### 研究动机

波音737 Max软件故障导致两起致命坠机事故，这一案例突显了多供应商模式在治理和协调方面的挑战。波音公司将飞行显示软件外包给HCL，软件测试外包给Cyient，软件集成与驾驶舱外包给Rockwell Collins。这种多供应商模式使得故障调查、根本原因识别和责任分配变得更加困难。

### 研究目的

本文的研究目的是探讨影响企业选择单一供应商或多供应商模式的IT服务外包决策的因素。具体而言，本文将考察新兴技术（如基于云的服务和自动化服务）、供应商位置（离岸或国内）以及经验学习（客户和供应商经验）对这一决策的影响。

### 理论框架

本文采用基于知识视角的理论框架，认为多供应商决策主要受两类因素驱动：一是协调和治理障碍，二是组织的技术、协调和治理能力。本文将通过分析2007年至2017年间全球签署的多份多年、数百万美元的IT服务合同数据库来验证这些假设。

### 研究方法

本文采用多变量回归分析方法，控制了合同规模、合同持续时间、定价类型、客户和供应商的经验等多个变量。通过这种方法，本文能够更准确地评估各因素对多供应商决策的影响。

### 预期贡献

本文的预期贡献包括：（1）深化对IT服务外包中单一供应商与多供应商决策驱动因素的理解；（2）为企业和供应商管理团队提供规范性建议；（3）扩展技术外包文献，特别是在新兴技术和多供应商模式方面的研究。

### 结论

本文通过对IT服务外包中单一供应商与多供应商决策的深入分析，揭示了新兴技术、供应商位置和经验学习等因素对这一决策的影响。研究结果不仅有助于理论界更好地理解IT服务外包的复杂性，也为企业管理层在实际操作中提供了有价值的指导。

---

### 第2章：Theoretical Background

## 第2章：Theoretical Background

### 引言

随着信息技术（IT）在组织成功中的核心地位日益凸显，技术管理成为运营管理的关键方面。然而，技术的快速发展和管理知识的复杂性使得企业在选择技术供应商时面临诸多挑战。本文通过知识基础理论的视角，探讨了影响企业选择单一供应商还是多供应商策略的因素。

### 知识基础理论的应用

知识基础理论强调知识管理和组织学习在技术采购中的重要性。本文采用这一理论框架，认为企业在选择供应商时需要考虑协调和治理障碍以及组织的技术、协调和治理能力。

### 初步访谈

为了深入了解技术外包的影响因素，作者进行了初步访谈。访谈对象包括客户组织、供应商组织和咨询公司的代表。访谈揭示了三个关键主题：

1. **新兴技术**：云计算和自动化服务正在改变企业采购IT的方式。
2. **地理位置**：由于创新和交付速度的需求，一些客户开始质疑与地理上遥远的供应商合作的效率。
3. **经验积累**：行业成熟度提高了客户和供应商在供应商选择、协调和治理方面的能力。

### 协调和治理障碍

协调和治理是分布式知识工作的核心挑战。本文分析了新兴技术和离岸供应商如何增加协调和治理的难度，从而影响单一供应商和多供应商策略的选择。

- **新兴技术**：新兴技术如云计算和自动化服务的知识新颖、模糊且隐性，增加了知识共享和协调的难度。
- **离岸供应商**：离岸供应商的使用增加了语言、文化和法律制度的差异，导致沟通和协调的挑战。

### 客户和供应商经验

经验是组织学习和能力发展的重要机制。本文探讨了客户和供应商的经验如何影响他们在单一供应商和多供应商策略之间的选择。

- **客户经验**：经验丰富的客户能够更好地管理多供应商关系，利用最佳供应商的优势。
- **供应商经验**：经验丰富的供应商能够更有效地整合系统组件，减少协调和治理的不确定性。

### 假设提出

基于上述分析，本文提出了以下假设：

1. **新兴技术与多供应商策略**：云计算服务与多供应商策略负相关，自动化服务与多供应商策略的关系不显著。
2. **离岸供应商与多供应商策略**：离岸供应商与多供应商策略负相关。
3. **客户经验与多供应商策略**：客户经验与多供应商策略正相关。
4. **供应商经验与多供应商策略**：供应商经验与多供应商策略负相关。

### 数据和方法

本文使用了2007年至2017年间全球签署的多百万美元IT服务合同数据库进行分析。数据集包含12,867份合同，涉及2359个独特供应商和8464个独特客户。通过多变量回归分析，验证了上述假设。

### 结论

本文通过知识基础理论的视角，深入探讨了影响企业选择单一供应商还是多供应商策略的因素。研究发现，新兴技术、离岸供应商、客户经验和供应商经验均对供应商选择策略有显著影响。这些发现为企业和技术供应商提供了重要的管理启示，有助于他们在复杂的IT外包环境中做出更明智的决策。

---

### 第3章：Hypotheses

## 第3章：假设

### 新兴技术与多源外包

新兴技术的引入带来了知识共享和协调的挑战，特别是在多供应商环境中。由于新兴技术的新颖性、模糊性和隐性知识特性，其在不同组织间的转移变得更加困难。这种知识共享的难度增加了跨组织协调的复杂性，从而影响了多源外包的决策。

- **云服务**：云服务的采用涉及复杂的技术迁移和数据安全问题，需要供应商与客户之间紧密的协调。多供应商环境下的云服务管理可能会增加协调负担，使得单源外包更具吸引力。
- **自动化服务**：自动化技术的应用需要高度定制化，且其技术能力和最佳实践尚未完全成熟。多供应商环境下的自动化服务部署可能会面临更大的协调和治理挑战。

### 离岸外包与多源外包

离岸外包增加了跨国界的协调和治理难度，语言、文化和法律制度的差异导致了沟通延迟和误解。这些因素使得多供应商环境下的离岸外包更加复杂和具有挑战性。

- **协调和治理障碍**：离岸外包不仅跨越了组织边界，还跨越了国家边界，增加了协调和治理的难度。多供应商环境下的离岸外包可能会进一步加剧这些挑战。
- **关系治理**：直接互动和社会化是建立关系治理的重要机制，但在地理距离较远的情况下，这种机制难以维持。

### 客户经验与多源外包

客户的经验积累提高了其在管理多供应商关系方面的能力。经验丰富的客户能够更好地搜索和评估供应商，监控供应商绩效，并有效整合多方服务。

- **市场搜索和评估**：经验丰富的客户能够更有效地搜索和评估多样化的供应商，提升最佳供应商策略的价值。
- **绩效监控和激励对齐**：经验丰富的客户能够建立有效的激励结构并监控供应商绩效，减少多供应商环境下的协调挑战。
- **整合和协调能力**：经验丰富的客户具备强大的整合能力，能够有效整合多方服务，提升整体业务效率。

### 供应商经验与多源外包

供应商的经验积累提升了其在特定技术和运营领域的能力，使得单源外包更具吸引力。经验丰富的供应商能够更好地整合系统组件，降低不确定性，并优化技术功能。

- **技术功能优化**：经验丰富的供应商能够通过多次业务合作积累经验，提升技术功能，减少因多供应商环境带来的技术功能下降。
- **系统集成能力**：经验丰富的供应商能够更好地整合系统组件，提升整体系统的协调性和一致性。
- **合同条款和风险管理**：经验丰富的供应商能够制定更完善的合同条款，提升风险管理能力，减少多供应商环境下的协调挑战。

通过以上分析，本章提出了五个假设，分别探讨了新兴技术、离岸外包、客户经验和供应商经验对多源外包决策的影响。这些假设为后续的实证研究提供了理论基础。

---

### 第4章：Data and Measures

## 数据来源

这篇论文的数据来源于国际数据公司(IDC)的服务合同数据库，该数据库记录了2007年至2017年间全球签订的多年、数百万美元的IT服务合同。IDC是一家领先的市场情报公司，其数据库被包括KPMG、Deloitte和EY在内的多家领先咨询公司用于行业洞察。

### 数据集特征

- **合同数量**：数据库包含12,867份合同，涉及2,359个独特供应商和8,464个独特客户。
- **时间范围**：数据涵盖2007年至2017年期间。
- **合同类型**：主要包含多年、数百万美元的合同，不包括通过现货市场交易购买的非常短期或高度商品化的技术服务。
- **数据处理**：为了构建经验变量，2007年至2009年签订的合同被排除在主要分析之外，最终数据库包含4,931份合同，涉及1,169个独特供应商和3,567个独特客户。

## 变量测量

### 因变量

- **多源采购（Multisourcing）**：一个二元变量，如果服务涉及多个供应商，则为1，否则为0。IDC将服务标记为多源采购，如果合同是涉及多个供应商的更大外包安排的一部分。

### 自变量

- **云服务（Cloud-based Services）**：一个二元变量，如果合同涉及基于云的技术，则为1。IDC提供了三种云服务的标识：基础设施即服务（IaaS）、平台即服务（PaaS）和软件即服务（SaaS）。
- **自动化服务（Automation Services）**：一个二元变量，如果合同描述中包含与自动化相关的关键词（如机器学习、人工智能、机器人流程自动化等），则为1。这些关键词是通过TextRank算法确定的。
- **离岸外包（Offshoring）**：一个二元变量，如果合同的工作是在与客户不同的国家进行的，则为1。

### 控制变量

- **先前关系（Prior Ties）**：一个二元变量，如果在签订焦点合同之前，供应商和客户之间有合同，则为1。
- **顶级云服务提供商（Top Cloud Vendor）**：一个二元变量，如果供应商是Gartner和Datamation年度排名中的顶级云服务提供商之一，则为1。
- **顶级自动化服务提供商（Top Automation Vendor）**：一个二元变量，如果供应商是Avasant年度排名中的顶级自动化服务提供商之一，则为1。
- **合同规模（Contract Scale）**：外包合同的金额（以百万美元计）。
- **子市场数量（Number of Submarkets）**：合同中涉及的独特活动数量，如应用开发或网络服务。
- **客户员工数量（Client Number of Employees）**：客户的员工总数。
- **合同持续时间（Contract Duration）**：基础合同的月数。
- **定价类型（Pricing Type）**：一个二元变量，如果合同是固定价格合同，则为1。
- **赫芬达尔-赫希曼指数（HHI）**：衡量合同范围内的供应市场竞争程度。
- **战略意图（Strategic Intent）**：一个二元变量，如果合同描述指向战略意图，则为1。
- **成本削减意图（Cost Reduction Intent）**：一个二元变量，如果合同描述指向成本削减意图，则为1。
- **年份虚拟变量（Year Dummies）**：控制时间特定效应的八个二元指标。

## 模型设定

论文使用了多向稳健聚类估计（MWRCE）方法来处理非独立同分布（non-i.i.d.）的观察数据。完整的模型如下：

$$
\begin{aligned}
\ln \left(\frac{P(\text{Multisourcing}=1)}{1 - P(\text{Multisourcing}=1)}\right) = \beta_0 + \beta_1 \text{Top cloud vendor} + \beta_2 \text{Top automation vendor} + \beta_3 \text{GNI per capita} + \beta_4 \text{Number of submarkets} \\
+ \beta_5 \text{Client number of employees} + \beta_6 \text{Contract scale} + \beta_7 \text{Pricing type} + \beta_8 \text{Year dummies} + \beta_9 \text{Contract duration} \\
+ \beta_{10} \text{HHI} + \beta_{11} (\text{Contract scale} \times \text{Number of submarkets}) + \beta_{12} \text{Prior ties} + \beta_{13} \text{STI focus} + \beta_{14} \text{Cost focus} \\
+ \beta_{15} \text{Cloud-based services} + \beta_{16} \text{Automation services} + \beta_{17} \text{Offshoring} + \beta_{18} \text{Client experience} + \beta_{19} \text{Vendor experience} + \epsilon
\end{aligned}
$$

其中，$\beta_0$ 是截距项，$\beta_1$ 到 $\beta_{19}$ 是回归系数，$\epsilon$ 是误差项。

## 结果分析

- **控制变量**：先前的关系与多源采购的可能性呈负相关，表明现有关系导致重复业务更可能是单源采购。合同规模与多源采购的可能性呈正相关，表明预算较大的合同更可能是多源采购。
- **自变量**：云服务与多源采购的可能性呈负相关，而自动化服务对多源采购的可能性没有显著影响。这表明只有云服务影响了多源采购决策。
- **其他发现**：离岸外包与多源采购的可能性呈负相关，表明离岸供应商不太可能参与多源采购安排。客户经验与多源采购的可能性呈正相关，而供应商经验与多源采购的可能性呈负相关。

## 稳健性检验

- **替代规格**：将多源采购合同合并为一个观察值，并使用权重平均生成转换后的自变量和协变量。结果与主要分析一致。
- **纵向稳健性**：将样本分为2012-2014年和2015-2017年两个子样本，分别进行logit分析。结果显示，客户经验的系数在两个子样本中统计上不同，但最近子样本的结果与主要分析一致。
- **细粒度措施**：将云服务和自动化服务分类为更细粒度的子服务，并创建相应的序数变量。结果显示，云服务的序数变量与多源采购的可能性呈负相关，而自动化服务的序数变量不显著。

## 敏感性分析

- **标准误聚类和数据过滤**：使用不同的标准误聚类方法和数据过滤条件进行分析，结果与主要分析一致。

## 内生性处理

- **工具变量**：使用供应商在其他客户中实施的云服务和自动化服务合同数量作为工具变量。结果表明，这些工具变量与内生变量显著相关，支持其有效性。

## 结论

这篇论文通过详细的实证分析，探讨了信息技术外包中单源采购与多源采购决策的影响因素。研究发现，云服务和客户经验对多源采购决策有显著影响，而离岸外包和供应商经验则对多源采购决策有负面影响。这些发现为信息技术外包的策略选择提供了重要的理论和实践指导。

---

### 第5章：Main Analysis and Results

## 第5章：主要分析和结果

### 数据描述

- **数据来源**：分析基于2007年至2017年间国际数据公司(IDC)服务合同数据库中的外包IT服务合同。
- **数据特征**：数据库主要包括多年、数百万美元的合同，不包括通过现货市场交易购买的非常短期或高度商品化的技术服务。
- **样本选择**：最终数据库包含4931份合同，涉及1169个独特供应商和3567个独特客户。

### 变量测量

- **因变量**：多源化（multisourcing），一个二元变量，如果服务涉及多个供应商则为1，否则为0。
- **自变量**：
  - 云服务（cloud-based services）：如果合同涉及云技术，则为1。
  - 自动化服务（automation services）：如果合同描述中包含自动化相关关键词，则为1。
  - 离岸外包（offshoring）：如果供应商工作在不同于客户的国家的则为1。
  - 客户经验（client experience）：客户在签订合同前在相关子市场的合同数量。
  - 供应商经验（vendor experience）：供应商在相关子市场的合同数量。
- **控制变量**：包括合同规模、合同持续时间、定价类型、客户员工数量、国家人均国民收入、赫芬达尔-赫希曼指数（HHI）、战略意图、成本削减意图等。

### 模型设定

- 使用多向稳健聚类（MWRCE）方法来处理非独立同分布（non-i.i.d.）的观察数据。
- 主模型方程如下：

$$
\begin{aligned}
\ln \left(\frac{P(\text{Multisourcing}=1)}{1-P(\text{Multisourcing}=1)}\right) = \beta_0 + \beta_1 \text{Top cloud vendor} + \beta_2 \text{Top automation vendor} + \beta_3 \text{GNI per capita} + \beta_4 \text{Number of submarkets} + \beta_5 \text{Client number of employees} + \beta_6 \text{Contract scale} + \beta_7 \text{Pricing type} + \beta_8 \text{Year dummies} + \beta_9 \text{Contract duration} + \beta_{10} \text{HHI} + \beta_{11} \text{Contract scale} \times \text{Number of submarkets} + \beta_{12} \text{Prior ties} + \beta_{13} \text{STI focus} + \beta_{14} \text{Cost focus} + \beta_{15} \text{Cloud-based services} + \beta_{16} \text{Automation services} + \beta_{17} \text{Offshoring} + \beta_{18} \text{Client experience} + \beta_{19} \text{Vendor experience} + \epsilon
\end{aligned}
$$

### 结果分析

- **控制变量的影响**：
  - 先前关系（Prior ties）与多源化的可能性呈负相关，表明现有关系导致重复业务更可能是单源化的。
  - 合同规模（Contract scale）与多源化的可能性呈正相关，表明预算较大的合同更可能是多源化的。
  - 合同持续时间（Contract duration）与多源化的可能性呈负相关，表明长期项目不太可能是多源化的。
  - 固定价格合同（Fixed-price contracts）更可能是多源化的。
  - 顶级自动化供应商（Top automation vendor）与多源化的可能性呈正相关。

- **自变量的影响**：
  - 云服务（Cloud-based services）与多源化的可能性呈负相关（β = 1.788, p < .05），表明云服务不太可能是多源化的。
  - 自动化服务（Automation services）对多源化的可能性没有显著影响。
  - 离岸外包（Offshoring）与多源化的可能性呈负相关（β = 0.692, p < .05），表明离岸供应商不太可能参与多源化安排。
  - 客户经验（Client experience）与多源化的可能性呈正相关（β = 0.247, p < .01），表明更有经验的客户更可能采用多源化策略。
  - 供应商经验（Vendor experience）与多源化的可能性呈负相关（β = 0.008, p < .10），表明更有经验的供应商不太可能参与多源化安排。

### 稳健性检验

- **替代模型规格**：将多源化合同合并为单一观察，并使用罕见事件逻辑回归方法（rare events logit method）进行处理，结果与主分析一致。
- **纵向稳健性检验**：将样本分为2012-2014年和2015-2017年两个子样本，结果表明客户经验的影响在最近子样本中显著。
- **细粒度措施**：将云服务和自动化服务分类为更具体的子服务类型，结果与主结果一致。
- **标准误聚类和数据过滤**：使用不同的标准误聚类方法和数据过滤条件，结果与主分析一致。

### 内生性检验

- **内生性问题**：客户采用新兴技术的决策可能是非随机的，可能与未观察到的因素相关，从而影响单源化和多源化决策。
- **解决方法**：同时估计云服务和自动化服务模型，以考虑误差项之间的相关性。使用供应商云服务（VendorCloud）和供应商自动化服务（VendorAutomation）作为工具变量，结果表明这些工具变量有效且显著。

### 事后分析

- **离岸外包的调节效应**：离岸外包对供应商经验与多源化之间的关系有显著的调节作用，表明国内供应商的经验减少多源化的可能性更大。
- **客户位置的调节效应**：客户所在国家的经济水平（以人均国民收入衡量）对自动化服务与多源化之间的关系有显著的调节作用，表明在经济发展水平较低的国家，自动化服务不太可能被多源化。

## 总结

本章通过详细的统计分析和稳健性检验，验证了新兴技术、供应商位置以及客户和供应商经验对IT服务单源化和多源化决策的影响。研究发现，云服务不太可能被多源化，而自动化服务的影响则更为复杂。此外，离岸外包和客户经验也对多源化决策有显著影响。这些发现为理解和指导IT服务外包策略提供了重要的理论和实践意义。

---

### 第6章：Robustness Checks

## 第6章：稳健性检验

### 数据集的独立性问题

在本研究中，数据集中的合同可能共享同一个客户或多个供应商，这导致了非独立同分布（non-i.i.d.）的观测值。使用简单的逻辑回归或概率回归模型会导致估计偏差，因为数据的非独立性。为了解决这个问题，研究者采用了多向鲁棒聚类（MWRCE）方法来处理非嵌套的双向聚类。

### 模型估计方法

研究者使用了Petersen（2009）提出的MWRCE方法的逻辑回归版本，并使用STATA 15.1中的logit2命令进行估计。具体来说，他们在客户公司ID和供应商公司ID上进行聚类，以确保估计结果的稳健性。

### 控制变量的影响

在模型7中，研究者发现了一些控制变量对多源化决策的显著影响：

- **先前的关系**：与供应商的先前关系与多源化的可能性呈负相关，表明现有关系导致重复业务更可能是单源化的。
- **合同规模**：合同规模与多源化的可能性呈正相关，表明预算较大的合同更可能是多源化的。
- **合同持续时间**：合同持续时间与多源化的可能性呈负相关，可能是因为长期项目涉及特定关系的投资策略，这在多源化安排中难以实现。
- **合同规模与子市场的交互项**：合同规模与子市场的交互项也与多源化的可能性呈负相关，表明随着复杂性的增加，多源化的可能性降低。
- **定价类型**：固定价格合同与多源化的可能性呈正相关，表明客户在大规模项目中使用固定价格合同以减少监控努力和对多个供应商的机会主义行为的对冲风险。
- **顶级自动化供应商**：顶级自动化供应商与多源化的可能性呈正相关，表明顶级自动化供应商在多源化决策中具有独特的影响。

### 多源化决策的主要结果

在模型7中，云服务与多源化的可能性呈负相关，而自动化服务对多源化的可能性没有显著影响。这支持了假设H1但不支持H2。

### 稀有事件逻辑回归

为了进一步评估主要结果的稳健性，研究者使用了稀有事件逻辑回归方法（King & Zeng, 2001, 2002），因为多源化决策在数据中是罕见的事件。这些结果与主要分析的结果一致。

### 纵向稳健性检验

研究者还进行了纵向稳健性检验，将数据分为两个子样本（2012-2014年和2015-2017年），并比较这两个子样本中假设变量的系数。结果显示，只有客户经验的系数在这两个子样本中存在统计差异，而其他假设变量的系数在最新的子样本（2015-2017年）中与主要分析一致。

### 更细粒度的措施

研究者还对云服务和自动化服务进行了更细粒度的分类，分别将其分为IaaS、PaaS、SaaS和RPA、AI-ML-CC。结果显示，云服务的细粒度分类与多源化的可能性呈负相关，而自动化服务的细粒度分类不显著。

### 替代标准误聚类和数据过滤

研究者还检查了不同标准误聚类和数据过滤的敏感性。结果显示，所有这些替代规格的结果与主要分析一致。

### 内生性问题

研究者还探讨了内生性问题，特别是客户和供应商在采用新兴技术时的非随机选择。他们使用了两阶段最小二乘法（2SLS）回归，并找到了有效的工具变量（VendorCloud和VendorAutomation），结果表明潜在的内生性并未影响主要结果。

### 结论

通过这些稳健性检验，研究者确认了其主要发现的稳健性，并提供了额外的证据支持其假设。

---

### 第7章：Endogeneity

## Endogeneity

### 1. 内生性问题概述

内生性问题是经济学和统计学中一个重要的概念，指的是模型中的解释变量与误差项之间存在相关性。这种相关性会导致估计结果的偏差，从而影响模型的可靠性和有效性。在内生性问题的处理上，研究者需要识别并控制这些潜在的内生变量，以确保估计结果的一致性和准确性。

### 2. 本研究中内生性的来源

在本研究中，客户采用新兴技术的决策可能是一个非随机选择。这些技术采纳决策可能与未观察到的因素相关，而这些因素也可能影响单一来源与多源采购决策。如果不对这种潜在的内生性进行适当控制，可能会导致估计结果的偏差。

### 3. 处理内生性的方法

为了处理这种潜在的内生性，研究者采用了联合模型来同时估计云服务和自动化服务的模型，并预测多源采购。具体方法包括：

- **工具变量法**：研究者使用了两个工具变量，分别是`VendorCloud`和`VendorAutomation`，分别用于预测云服务和自动化服务。工具变量的选择需要满足两个条件：一是与误差项不相关（排除条件），二是与内生变量显著相关（相关性条件）。
- **两阶段最小二乘法（2SLS）**：研究者使用了`ivreg2`命令进行2SLS回归，以验证工具变量的有效性和预测能力。

### 4. 工具变量的有效性验证

- **VendorCloud**：该变量捕捉了选定供应商在前一年实施的云服务合同数量。由于IT服务供应商认识到在其他客户中主动实施新兴技术可以使其保持相关性并提升服务价值链，因此`VendorCloud`与云技术在焦点合同中的使用相关。
- **VendorAutomation**：类似地，该变量捕捉了选定供应商在前一年实施的自动化服务合同数量。

### 5. 结果与讨论

- **工具变量的显著性**：两个工具变量与其相应的内生变量显著相关（p < .001），支持其作为有效工具变量的使用。
- **假设检验结果**：使用工具变量法的假设检验结果与主要MWRCE分析结果一致，这使研究者对云服务和自动化服务潜在内生性不影响主要结果的信心增强。

### 6. 结论

通过处理内生性问题，研究者确保了模型估计结果的可靠性和有效性。这一过程不仅增强了研究的科学性，也为后续研究提供了方法论上的参考。

---

### 第8章：Post Hoc Analyses

## Post Hoc Analyses

### 1. 引言

在第8章节中，作者进行了事后分析（Post Hoc Analyses），以进一步探讨供应商和客户地理位置对单一来源与多源决策的影响。这些分析旨在更深入地理解地理位置如何影响信息技术服务的外包决策。

### 2. 研究背景

之前的研究表明，跨国界工作会增加协调和治理的难度。然而，供应商工作地点的特征以及决策者的地理位置可能会调节其他因素的影响。因此，作者通过探索性分析进一步探讨了供应商和客户位置对单一来源与多源决策的调节效应。

### 3. 方法论

作者使用了交互作用项来检验供应商和客户位置的调节效应。具体来说，他们将离岸外包与其他假设变量进行交互，并分析了其对多源决策的影响。此外，他们还考察了客户所在国家的经济水平（用人均国民总收入GNI per capita表示）对自动化服务与多源决策之间关系的影响。

### 4. 结果

#### 4.1 离岸外包的调节效应

- **供应商经验与多源决策的关系**：研究发现，离岸外包对供应商经验与多源决策之间的关系具有显著的调节效应。具体而言，对于国内合同，供应商经验显著降低了被纳入多源安排的可能性；而对于离岸合同，这种关系并不显著。
  
  - **图表展示**：图4a展示了供应商经验与离岸外包的交互效应。可以看出，随着供应商经验的增加，国内合同的多元采购可能性下降得更快，而离岸合同的多元采购可能性则没有显著变化。

#### 4.2 客户位置的调节效应

- **自动化服务与多源决策的关系**：研究发现，客户所在国家的经济水平对自动化服务与多源决策之间的关系具有显著的调节效应。具体而言，对于经济水平较高的客户，自动化服务对多源决策没有显著影响；而对于经济水平较低的客户，自动化服务显著降低了多源决策的可能性。
  
  - **图表展示**：图4b展示了人均国民总收入与自动化服务的交互效应。可以看出，对于高收入国家，自动化服务对多源决策的影响不显著；而对于低收入国家，自动化服务显著降低了多源决策的可能性。

### 5. 讨论

#### 5.1 理论意义

- 这些发现为分布式知识工作的协调和治理挑战提供了新的见解。特别是，它们表明新兴经济体中的客户在采用涉及自动化的多源策略时面临更大的协调和治理风险。
- 这些结果也强调了地理位置在技术外包决策中的重要性，尤其是在新兴经济体中。

#### 5.2 管理启示

- 对于供应商而言，了解客户所在国家的经济水平和地理位置可以帮助他们更好地定位自己的服务。例如，在新兴经济体中，专注于单一来源合同的供应商可能会更有优势。
- 对于客户而言，这些发现提醒他们在制定外包策略时需要考虑地理位置和经济发展水平的影响，特别是在涉及复杂技术（如自动化）时。

### 6. 结论

通过这些事后分析，作者揭示了地理位置在信息技术服务外包决策中的重要性。特别是，他们发现离岸外包和客户所在国家的经济水平对多源决策具有显著的调节效应。这些发现不仅丰富了现有的理论文献，还为实践中的外包决策提供了重要的指导。

---

### 第9章：Discussion and Conclusions

## 第9章：讨论与结论

### 1. 研究贡献

本文通过技术外包文献和行业专家访谈，探讨了影响信息技术（IT）服务单一来源与多源决策的两个主要因素集：新兴技术和供应商位置导致的协调和治理障碍，以及客户和供应商经验带来的技术、协调和治理能力。研究发现，云服务和自动化服务对多源决策的影响不同，离岸供应商不太可能参与多源安排，而更有经验的客户更倾向于采用多源策略，而有经验的供应商则相反。

### 2. 理论意义

本文直接推进了管理技术文献，特别是关于影响外包知识密集型服务设计和结构因素的研究。通过实证研究，本文提供了关于新兴技术、供应商位置以及客户和供应商经验如何影响IT服务多源决策的证据。这些发现不仅丰富了现有的技术外包理论，还为分布式知识工作领域提供了新的见解。

### 3. 实践意义

对于供应商而言，本文指出云服务的单一来源趋势是一把双刃剑。有强大云能力的供应商可能会获得更多业务，而没有这些能力的供应商可能会失去整个客户关系。对于自动化服务，本文建议供应商在新兴经济体中寻找机会，因为这些地区的客户更倾向于单一来源。此外，本文还强调了地理位置对供应商决策的影响，建议供应商根据目标客户的位置调整其运营策略。

对于客户而言，本文建议有经验的客户应考虑多源策略的技术优势，而缺乏经验的客户则应选择单一来源以降低协调和治理风险。随着云和自动化技术的成熟，客户可能会发现多源策略更具吸引力。

### 4. 研究局限性与未来方向

尽管本文提供了许多有价值的见解，但仍存在一些局限性。例如，客户的过往单一或多源经验可能影响其后续决策，这是一个值得未来研究的方向。此外，自动化服务对多源决策的影响在不同经济体的客户中表现不同，这也需要进一步探讨。未来的研究还可以深入分析供应商如何应对客户对多源策略日益增长的偏好，以及这种偏好如何随时间演变。

### 5. 总结

本文通过实证研究，揭示了新兴技术、供应商位置以及客户和供应商经验对IT服务单一来源与多源决策的影响。这些发现不仅为技术外包领域的理论发展提供了支持，也为实践中的供应商和客户提供了重要的指导。未来的研究可以在此基础上进一步拓展，以更好地理解和应对不断变化的外包环境。

---

## 📊 分析统计

- **综合分析**: ✅ 已完成
- **章节分析**: 9 个章节
- **总分析数**: 10 项
- **分析引擎**: 腾讯混元API
- **分析模式**: 多提示词深度分析

---

*本分析由腾讯混元API自动生成，采用多轮对话模式进行深度学术分析*
