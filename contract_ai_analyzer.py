#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同AI系统影响分析工具包
Contract AI Impact Analysis Toolkit

作者: AI Assistant
日期: 2024
用途: 分析合同AI系统对项目落实和风险管理的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ContractAIAnalyzer:
    """合同AI影响分析器"""
    
    def __init__(self, project_data_path=None):
        """
        初始化分析器
        
        Args:
            project_data_path: 项目数据文件路径
        """
        self.project_data = None
        self.ai_usage_data = None
        self.risk_data = None
        self.analysis_results = {}
        
        if project_data_path:
            self.load_project_data(project_data_path)
    
    def load_project_data(self, file_path):
        """加载项目数据"""
        try:
            self.project_data = pd.read_csv(file_path, encoding='utf-8')
            print(f"成功加载项目数据，共{len(self.project_data)}条记录")
            return True
        except Exception as e:
            print(f"加载项目数据失败: {e}")
            return False
    
    def load_ai_usage_data(self, file_path):
        """加载AI使用数据"""
        try:
            self.ai_usage_data = pd.read_csv(file_path, encoding='utf-8')
            print(f"成功加载AI使用数据，共{len(self.ai_usage_data)}条记录")
            return True
        except Exception as e:
            print(f"加载AI使用数据失败: {e}")
            return False
    
    def load_risk_data(self, file_path):
        """加载风险数据"""
        try:
            self.risk_data = pd.read_csv(file_path, encoding='utf-8')
            print(f"成功加载风险数据，共{len(self.risk_data)}条记录")
            return True
        except Exception as e:
            print(f"加载风险数据失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        if self.project_data is None:
            print("请先加载项目数据")
            return False
        
        # 数据清洗和转换
        df = self.project_data.copy()
        
        # 转换日期字段
        date_columns = ['售前立项时间', '项目创建时间', '启动时间', '合同签订时间', 
                       '计划开始时间', '实际开始时间', '计划结束时间', '实际结束时间']
        
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 计算关键指标
        df = self._calculate_key_metrics(df)
        
        self.project_data = df
        print("数据预处理完成")
        return True
    
    def _calculate_key_metrics(self, df):
        """计算关键绩效指标"""
        
        # 1. 时间效率指标
        df['项目周期'] = (df['实际结束时间'] - df['实际开始时间']).dt.days
        df['计划周期'] = (df['计划结束时间'] - df['计划开始时间']).dt.days
        df['周期偏差率'] = (df['项目周期'] - df['计划周期']) / df['计划周期']
        df['按时完成'] = df['周期偏差率'] <= 0
        
        # 2. 成本效率指标
        df['成本偏差率'] = (df['已投成本'] - df['预算成本']) / df['预算成本']
        df['成本控制达标'] = df['成本偏差率'] <= 0.1
        
        # 3. 人天效率指标
        df['人天效率'] = df['已投人天'] / df['预算人天']
        df['人天控制达标'] = df['人天效率'] <= 1.1
        
        # 4. 质量指标
        df['签字及时性综合得分'] = (df['方案签字及时性得分'].fillna(0) + 
                               df['UAT签字及时性得分'].fillna(0) + 
                               df['上线签字及时性得分'].fillna(0) + 
                               df['验收签字及时性得分'].fillna(0)) / 4
        
        # 5. 综合绩效得分
        df['综合绩效得分'] = self._calculate_composite_score(df)
        
        return df
    
    def _calculate_composite_score(self, df):
        """计算综合绩效得分"""
        # 标准化各项指标到0-100分
        time_score = np.where(df['周期偏差率'] <= 0, 100, 
                             np.maximum(0, 100 - df['周期偏差率'] * 100))
        
        cost_score = np.where(df['成本偏差率'] <= 0, 100,
                             np.maximum(0, 100 - df['成本偏差率'] * 100))
        
        quality_score = df['签字及时性综合得分'].fillna(50)
        
        satisfaction_score = df['满意度评分'].fillna(5) * 10
        
        # 加权平均 (时间30% + 成本30% + 质量20% + 满意度20%)
        composite_score = (time_score * 0.3 + cost_score * 0.3 + 
                          quality_score * 0.2 + satisfaction_score * 0.2)
        
        return composite_score
    
    def analyze_project_performance(self, ai_start_date=None):
        """分析项目绩效表现"""
        if self.project_data is None:
            print("请先加载和预处理数据")
            return None
        
        df = self.project_data.copy()
        
        # 如果提供了AI启用日期，进行前后对比
        if ai_start_date:
            ai_start = pd.to_datetime(ai_start_date)
            df['AI期间'] = df['项目创建时间'] >= ai_start
            
            before_ai = df[df['AI期间'] == False]
            after_ai = df[df['AI期间'] == True]
            
            performance_comparison = {
                'AI前项目数量': len(before_ai),
                'AI后项目数量': len(after_ai),
                'AI前平均绩效得分': before_ai['综合绩效得分'].mean(),
                'AI后平均绩效得分': after_ai['综合绩效得分'].mean(),
                'AI前按时完成率': before_ai['按时完成'].mean() * 100,
                'AI后按时完成率': after_ai['按时完成'].mean() * 100,
                'AI前成本控制达标率': before_ai['成本控制达标'].mean() * 100,
                'AI后成本控制达标率': after_ai['成本控制达标'].mean() * 100,
                'AI前平均满意度': before_ai['满意度评分'].mean(),
                'AI后平均满意度': after_ai['满意度评分'].mean()
            }
            
            # 计算改善幅度
            performance_comparison['绩效得分改善'] = (
                performance_comparison['AI后平均绩效得分'] - 
                performance_comparison['AI前平均绩效得分']
            )
            
            performance_comparison['按时完成率改善'] = (
                performance_comparison['AI后按时完成率'] - 
                performance_comparison['AI前按时完成率']
            )
            
            # 统计显著性检验
            if len(before_ai) > 0 and len(after_ai) > 0:
                t_stat, p_value = stats.ttest_ind(
                    after_ai['综合绩效得分'].dropna(),
                    before_ai['综合绩效得分'].dropna()
                )
                performance_comparison['统计显著性_t值'] = t_stat
                performance_comparison['统计显著性_p值'] = p_value
                performance_comparison['是否显著改善'] = p_value < 0.05
            
            self.analysis_results['项目绩效分析'] = performance_comparison
            return performance_comparison
        
        else:
            # 整体绩效分析
            overall_performance = {
                '总项目数量': len(df),
                '平均绩效得分': df['综合绩效得分'].mean(),
                '按时完成率': df['按时完成'].mean() * 100,
                '成本控制达标率': df['成本控制达标'].mean() * 100,
                '平均满意度': df['满意度评分'].mean(),
                '平均回款率': df['回款率'].mean()
            }
            
            self.analysis_results['整体绩效分析'] = overall_performance
            return overall_performance
    
    def analyze_risk_management(self):
        """分析风险管理效果"""
        if self.risk_data is None:
            print("请先加载风险数据")
            return None
        
        risk_df = self.risk_data.copy()
        
        # 风险预测准确性分析
        if '人工确认结果' in risk_df.columns:
            accuracy_metrics = {
                '总预警数量': len(risk_df),
                '准确预警数量': len(risk_df[risk_df['人工确认结果'] == '确认']),
                '误报数量': len(risk_df[risk_df['人工确认结果'] == '误报']),
                '预警准确率': len(risk_df[risk_df['人工确认结果'] == '确认']) / len(risk_df) * 100,
                '误报率': len(risk_df[risk_df['人工确认结果'] == '误报']) / len(risk_df) * 100
            }
        
        # 风险等级分布
        if '风险等级' in risk_df.columns:
            risk_level_dist = risk_df['风险等级'].value_counts()
            accuracy_metrics['风险等级分布'] = risk_level_dist.to_dict()
        
        # 风险类型分析
        if '风险类型' in risk_df.columns:
            risk_type_dist = risk_df['风险类型'].value_counts()
            accuracy_metrics['风险类型分布'] = risk_type_dist.to_dict()
        
        self.analysis_results['风险管理分析'] = accuracy_metrics
        return accuracy_metrics
    
    def calculate_roi(self, ai_investment_cost, annual_maintenance_cost):
        """计算投资回报率"""
        if '项目绩效分析' not in self.analysis_results:
            print("请先进行项目绩效分析")
            return None
        
        perf_data = self.analysis_results['项目绩效分析']
        
        # 估算年度收益
        avg_project_value = self.project_data['合同额'].mean()
        annual_projects = len(self.project_data)  # 假设为年度项目数
        
        # 效率提升带来的收益
        efficiency_improvement = perf_data.get('绩效得分改善', 0) / 100
        efficiency_benefit = efficiency_improvement * avg_project_value * annual_projects
        
        # 风险减少带来的收益（假设风险损失为合同额的5%）
        risk_reduction_benefit = 0
        if '风险管理分析' in self.analysis_results:
            risk_data = self.analysis_results['风险管理分析']
            risk_accuracy = risk_data.get('预警准确率', 0) / 100
            risk_reduction_benefit = risk_accuracy * 0.05 * avg_project_value * annual_projects
        
        # 总收益
        total_annual_benefit = efficiency_benefit + risk_reduction_benefit
        
        # ROI计算
        total_cost = ai_investment_cost + annual_maintenance_cost
        roi = (total_annual_benefit - annual_maintenance_cost) / ai_investment_cost * 100
        payback_period = ai_investment_cost / (total_annual_benefit - annual_maintenance_cost)
        
        roi_analysis = {
            'AI投资成本': ai_investment_cost,
            '年度维护成本': annual_maintenance_cost,
            '效率提升收益': efficiency_benefit,
            '风险减少收益': risk_reduction_benefit,
            '年度总收益': total_annual_benefit,
            '年度净收益': total_annual_benefit - annual_maintenance_cost,
            'ROI百分比': roi,
            '投资回收期(年)': payback_period,
            '成本效益比': total_annual_benefit / total_cost
        }
        
        self.analysis_results['ROI分析'] = roi_analysis
        return roi_analysis
    
    def generate_visualizations(self, save_path=None):
        """生成可视化图表"""
        if self.project_data is None:
            print("请先加载数据")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('合同AI系统影响分析报告', fontsize=16, fontweight='bold')
        
        # 1. 项目绩效对比
        if 'AI期间' in self.project_data.columns:
            perf_by_ai = self.project_data.groupby('AI期间')['综合绩效得分'].mean()
            axes[0,0].bar(['AI前', 'AI后'], perf_by_ai.values, color=['lightcoral', 'lightblue'])
            axes[0,0].set_title('项目绩效得分对比')
            axes[0,0].set_ylabel('平均绩效得分')
            
            # 添加数值标签
            for i, v in enumerate(perf_by_ai.values):
                axes[0,0].text(i, v + 1, f'{v:.1f}', ha='center', va='bottom')
        
        # 2. 按时完成率对比
        if 'AI期间' in self.project_data.columns:
            completion_by_ai = self.project_data.groupby('AI期间')['按时完成'].mean() * 100
            axes[0,1].bar(['AI前', 'AI后'], completion_by_ai.values, color=['lightcoral', 'lightblue'])
            axes[0,1].set_title('按时完成率对比')
            axes[0,1].set_ylabel('按时完成率 (%)')
            
            for i, v in enumerate(completion_by_ai.values):
                axes[0,1].text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
        
        # 3. 成本控制情况
        cost_control = self.project_data['成本控制达标'].value_counts()
        axes[1,0].pie(cost_control.values, labels=['超预算', '控制达标'], autopct='%1.1f%%',
                     colors=['lightcoral', 'lightgreen'])
        axes[1,0].set_title('成本控制达标情况')
        
        # 4. 满意度分布
        if '满意度评分' in self.project_data.columns:
            satisfaction_data = self.project_data['满意度评分'].dropna()
            axes[1,1].hist(satisfaction_data, bins=10, color='skyblue', alpha=0.7, edgecolor='black')
            axes[1,1].set_title('客户满意度分布')
            axes[1,1].set_xlabel('满意度评分')
            axes[1,1].set_ylabel('项目数量')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def generate_report(self, output_file=None):
        """生成分析报告"""
        report_content = []
        report_content.append("# 合同AI系统影响分析报告\n")
        report_content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 执行摘要
        report_content.append("## 执行摘要\n")
        if '项目绩效分析' in self.analysis_results:
            perf_data = self.analysis_results['项目绩效分析']
            report_content.append(f"- 分析项目总数: {perf_data.get('AI前项目数量', 0) + perf_data.get('AI后项目数量', 0)}\n")
            report_content.append(f"- 绩效得分改善: {perf_data.get('绩效得分改善', 0):.2f}分\n")
            report_content.append(f"- 按时完成率改善: {perf_data.get('按时完成率改善', 0):.2f}%\n")
            if perf_data.get('是否显著改善', False):
                report_content.append("- 改善效果具有统计显著性\n")
        
        # 详细分析结果
        for analysis_type, results in self.analysis_results.items():
            report_content.append(f"\n## {analysis_type}\n")
            for key, value in results.items():
                if isinstance(value, (int, float)):
                    if 'rate' in key.lower() or '率' in key or '比' in key:
                        report_content.append(f"- {key}: {value:.2f}%\n")
                    else:
                        report_content.append(f"- {key}: {value:.2f}\n")
                else:
                    report_content.append(f"- {key}: {value}\n")
        
        report_text = ''.join(report_content)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"报告已保存到: {output_file}")
        
        return report_text

# 使用示例
if __name__ == "__main__":
    # 创建分析器实例
    analyzer = ContractAIAnalyzer()
    
    # 加载数据（请根据实际文件路径调整）
    # analyzer.load_project_data("Data_Structure/Project_Management_Related_Dataset.csv")
    
    # 数据预处理
    # analyzer.preprocess_data()
    
    # 分析项目绩效（假设AI系统从2024年1月1日开始使用）
    # performance_results = analyzer.analyze_project_performance(ai_start_date="2024-01-01")
    
    # 计算ROI（假设投资100万，年维护费20万）
    # roi_results = analyzer.calculate_roi(ai_investment_cost=1000000, annual_maintenance_cost=200000)
    
    # 生成可视化图表
    # analyzer.generate_visualizations(save_path="contract_ai_analysis_charts.png")
    
    # 生成分析报告
    # report = analyzer.generate_report(output_file="contract_ai_impact_report.md")
    
    print("合同AI影响分析工具包已准备就绪！")
    print("请按照注释中的示例代码使用各项功能。")
