# Diffusion of Information Technology Outsourcing Influence Sources and the Kodak Effect

**分析时间**: 2025-07-18 23:19:17
**原文件**: pdf_paper\Loh和Venkatraman - 1992 - Diffusion of Information Technology Outsourcing Influence Sources and the Kodak Effect.pdf
**文件ID**: file-EZgalByiJt9IM9uGMGyJREBi

---

## 📋 综合分析

# 一句话总结  
这篇论文通过实证研究揭示了美国企业IT外包扩散的主要驱动力来自内部模仿行为，而非外部影响，并发现Kodak与IBM的外包合作事件显著强化了这一内部影响效应。

# 论文概览  
- **研究背景和动机**：  
  随着信息技术（IT）治理模式从传统层级制向混合模式（如外包）转变，IT外包成为企业战略创新。论文旨在探究IT外包扩散的驱动因素，尤其是内部模仿与外部影响的相对作用。  

- **主要研究问题**：  
  1. IT外包扩散的主要影响来源是什么？  
  2. Kodak-IBM外包合同这一关键事件是否改变了内外部影响的相对重要性？  

- **研究方法概述**：  
  通过电子文献计量搜索获取1988年4月至1990年8月间的60个外包案例，采用非线性最小二乘法（NLS）估计扩散模型，并以Kodak事件为分界点划分“前Kodak”和“后Kodak”两个时期进行对比分析。  

- **核心贡献和创新点**：  
  - 首次将扩散理论应用于IT外包这一管理创新，提出内部模仿是主要驱动力。  
  - 通过关键事件分析法验证了Kodak事件的“示范效应”，丰富了创新扩散研究的方法论。  

# 逐章详细分析  

## 1. Introduction（引言）  
- **主要内容**：  
  介绍IT外包的兴起背景（如Dearden预言IT部门“萎缩”）、市场规模预测（Yankee Group估算1994年外包收入达490亿美元），以及学术界和产业界对IT外包的关注。  

- **关键概念和理论**：  
  - IT外包定义：用户组织将IT基础设施的控制权或决策权部分或全部转移给外部供应商。  
  - 管理创新视角：引用Teece和Mahajan的定义，强调IT外包是组织治理模式的根本性转变。  

- **与其他章节的逻辑关系**：  
  为后续理论框架（第2章）和实证分析（第5章）奠定背景，明确研究问题。  

## 2. Theoretical Perspectives（理论视角）  
- **主要内容**：  
  分析IT外包作为管理创新的三个维度：  
  1. 治理模式转变（从层级制到混合伙伴关系）。  
  2. 组织内部流程重构（如角色重新分配）。  
  3. 外部环境互动方式变化（从短期合同到长期联盟）。  

- **关键概念和理论**：  
  - 行政创新（Administrative Innovation）：Venkatraman定义为涉及组织常规的重大变革。  
  - 制度同构理论（DiMaggio & Powell）：解释组织为何模仿同行行为以应对不确定性。  

- **与其他章节的逻辑关系**：  
  为第3章的扩散模型选择提供理论依据，尤其是内部影响模型的合理性。  

## 3. A Diffusion-of-Innovation Perspective（创新扩散视角）  
- **主要内容**：  
  回顾Rogers的创新扩散理论，强调社会系统、传播渠道和时间三个要素，并指出IT外包的扩散需结合水平（同行间）和垂直（供应商-用户）传播渠道。  

- **关键概念和理论**：  
  - 扩散模型分类：内部影响（模仿先行者）、外部影响（供应商/媒体推动）、混合影响。  

- **与其他章节的逻辑关系**：  
  直接引出第4章的研究方法设计，明确模型比较的框架。  

## 4. Influence Sources in the Diffusion of IT Outsourcing（影响来源分析）  
- **主要内容**：  
  对比三种扩散模型的数学表达式：  
  1. 内部影响模型：$dN(t)/dt = qN(t)[m - N(t)]$（模仿驱动）。  
  2. 外部影响模型：$dN(t)/dt = p[m - N(t)]$（外部信息驱动）。  
  3. 混合影响模型：$dN(t)/dt = [p + qN(t)][m - N(t)]$。  

- **关键概念和理论**：  
  - 模型参数意义：$q$反映内部模仿强度，$p$反映外部影响强度。  

- **与其他章节的逻辑关系**：  
  为第5章的实证检验提供理论工具，尤其是区分内外部影响的统计方法。  

## 5. Kodak's Outsourcing as a Critical Event（关键事件分析）  
- **主要内容**：  
  以Kodak-IBM合同（1989年7月）为分界点，划分“前Kodak”和“后Kodak”时期，检验Kodak事件是否强化了内部影响。  

- **关键概念和理论**：  
  - 关键事件效应：引用Rogers和Kincaid的观点，认为意见领袖的决策会降低其他组织的采纳不确定性。  

- **与其他章节的逻辑关系**：  
  回应第1章的研究问题2，验证第4章模型的实际应用效果。  

## 6. Methods（方法论）  
- **主要内容**：  
  - 数据来源：通过UMI数据库搜索1988-1990年的外包合同，筛选出60个案例。  
  - 模型估计：采用非线性最小二乘法（NLS）和J检验、P检验比较模型拟合优度。  

- **关键概念和理论**：  
  - 白噪声模型（Null Hypothesis）：作为基准比较的随机扩散假设。  

- **与其他章节的逻辑关系**：  
  支撑第5章的实证结果，确保分析方法的严谨性。  

## 7. Results（结果）  
- **主要内容**：  
  - 全时期分析：内部影响模型显著优于外部影响模型（P检验p<0.01）。  
  - 分时期分析：前Kodak时期无显著差异，后Kodak时期内部影响占主导（P检验p<0.05）。  

- **关键发现**：  
  Kodak事件显著提升了内部模仿的相对重要性，验证了“Kodak效应”。  

- **与其他章节的逻辑关系**：  
  直接回答第1章的两个研究问题，总结全文核心结论。  

## 8. Discussion（讨论）  
- **主要内容**：  
  - 理论贡献：支持DiMaggio和Powell的制度同构理论，解释不确定性下组织的模仿行为。  
  - 实践意义：企业可通过观察同行成功案例降低IT外包的风险感知。  

- **局限性**：  
  样本仅包含大额合同，可能忽略小型外包决策的影响。  

- **与其他章节的逻辑关系**：  
  延伸第2章的理论讨论，提出未来研究方向（如引入社会网络分析）。  

# 总体评价  
- **优势和局限性**：  
  - 优势：首次系统实证IT外包扩散机制，方法论严谨（非线性估计+关键事件分析）。  
  - 局限性：样本偏差（依赖媒体报道的大额合同）、未考虑行业异质性。  

- **影响和意义**：  
  为IT治理研究提供了扩散理论的经典范例，启发了后续关于数字化转型的研究（如云计算采纳）。  

- **未来研究建议**：  
  1. 扩展数据来源（如访谈补充小额合同）。  
  2. 引入动态扩散模型（如放宽恒定采纳人口假设）。  
  3. 探索社会结构（如企业间董事联结）对扩散路径的影响。

---

## 📚 逐章节详细分析

以下是对论文各章节的深入分析，每个章节都经过了专门的AI分析：

### 第1章：Introduction

# 第1章：Introduction 详细分析

## 研究背景与趋势

### IT外包的兴起

- **定义**：IT外包（IT outsourcing）指的是用户组织将IT基础设施的物理和/或人力资源的部分或全部控制权和决策权转移给外部组织（如技术供应商或系统集成商）的过程。
- **市场趋势**：Dearden (1987) 预测IT组织将“逐渐消失”，因为终端用户对计算环境的控制增加，外部软件专家越来越多地负责企业系统开发。这一趋势导致了数十亿美元的IT外包市场。
- **市场预测**：The Yankee Group估计1994年IT外包总收入将达到490亿美元，G2 Research和Input, Inc.预测1990年后5年内IT外包的年增长率约为17%。

### IT外包的商业环境

- **需求方**：市场涵盖了营利性和非营利性部门的组织，这些不同部门的用户通常由同一供应商服务。
- **供应方**：行业由前五大供应商主导：Andersen Consulting、Computer Sciences Corp. (CSC)、Digital Equipment Corp. (DEC)、Electronic Data Systems Corp. (EDS) 和 International Business Machine Corp. (IBM)。这些供应商提供跨多个行业和部门的全方位外包服务。

## 研究动机与重要性

### IT外包作为战略选择

- **市场关注**：随着IT外包作为可行战略选项的重要性增加，这一创新的管理实践在专业社区中受到了广泛关注。
- **市场推动**：供应商积极管理用户的IT基础设施。例如，Martin Marietta Information Systems Group的广告语：“你不需要为了电力而拥有一个发电厂……为什么需要为了信息系统而拥有一个数据中心？”
- **媒体覆盖**：Computerworld和Information Week等领先行业出版物对IT外包现象进行了广泛报道。Merrill Lynch的外包分析师称这一行业为“一个巨大的、蓬勃发展的雪球滚下山坡”。

## 研究问题与方法

### Kodak事件的影响

- **Kodak-IBM合同**：1989年7月，Kodak宣布与IBM签订一项独特合同，IBM将接管Kodak的四个数据中心，300名Kodak员工将成为IBM员工。Kodak希望通过此举削减高达50%的成本。
- **影响**：这一涉及两家知名公司的大合同对IT外包产生了深远影响，使其成为企业的一个严肃战略选择，“考虑外包”成为1990年代成功（或生存）的十大问题之一。

### 研究目标

- **研究问题1**：什么影响因素最能描述IT外包的扩散？
- **研究问题2**：Kodak-IBM合同前后，什么影响因素最能描述IT外包的扩散？

## 理论框架

### IT外包作为管理创新

- **定义**：IT外包被视为一种管理创新，因为它代表了组织治理模式的根本转变，从传统的层级控制和与供应商的市场交易转向混合模式或伙伴关系。
- **变化**：IT外包带来了用户组织内部流程的重大变化，包括重新定义联络角色、新的决策机制、资源分配和绩效评估标准的变化。

### 扩散理论

- **定义**：创新扩散是指创新通过特定渠道在社会系统中随时间传播的过程。
- **要素**：创新、社会系统、传播渠道和时间。
- **应用**：扩散理论已被用于理解各种技术和行政创新的扩散模式，如现代软件实践、电子表格软件、BITNET和数据库设计工具。

## 结论

- **研究贡献**：本文通过纵向方法探讨IT外包作为行政创新的扩散过程中的影响因素，应用扩散模型分析IT外包的扩散模式。
- **方法改进**：通过更强大的非线性估计和特定规范测试，改进了现有的扩散模型。
- **研究设计**：通过电子文献计量搜索过程生成了美国60个外包合同的综合样本，分析了Kodak事件前后IT外包的扩散模式。

通过对第1章的详细分析，可以看出本文的研究背景、动机、理论框架和研究方法都为后续的实证分析奠定了坚实的基础。

---

### 第2章：Theoretical Perspectives

# 第2章：Theoretical Perspectives 详细分析

## IT Outsourcing as an Administrative Innovation

### 定义与背景

论文首先将IT外包定义为一种组织治理模式的转变，从传统的层级控制和与供应商的市场交易转向新的混合模式或伙伴关系。这种转变不仅仅是技术上的变化，更是组织内部和外部管理方式的根本性变革。

- **定义**：IT外包是指用户组织将IT基础设施的物理和/或人力资源的显著部分转移给外部供应商，如技术供应商或系统集成商。
- **背景**：随着终端用户对计算环境的控制增强，以及外部软件专家越来越多地负责企业系统开发，IT治理正在发生显著变化。

### 行政创新的特征

论文引用了Teece (1980)和Mahajan et al. (1988)的定义，认为行政创新涉及组织内部安排和外部对齐的例行程序的重大变化。具体来说，IT外包作为行政创新具有以下特征：

- **首次采纳**：组织首次采用新的政策或实践。
- **重大变革**：涉及大量的设置成本和组织中断。
- **全面视角**：将行政任务视为组织与环境的一致性。

### 支持IT外包作为行政创新的论据

论文通过以下几个方面支持将IT外包视为行政创新：

#### 治理模式的根本转变

- **从层级控制到混合模式**：传统的内部控制和协调模式转向新的混合模式或伙伴关系，如与供应商的联盟和合作关系。
- **战略和运营机制的转变**：这种治理模式的转变需要组织在当前使命或范围内重新定位其战略和运营机制。

#### 内部流程的重大变化

- **内部角色和关系的变化**：多组织伙伴关系改变了进入这些伙伴关系的组织的内部角色、关系和权力动态。
- **内部例程的调整**：例如，IT部门的经理可能需要学习全新的技能，以监督外包合同的执行。

#### 外部环境处理的变化

- **从“臂长”关系到伙伴关系**：从与IT市场的“臂长”关系（多个竞争性投标和价格主导的短期合同）转向“准公司”模式（涉及少数选定合作伙伴的长期联盟）。
- **共同开发和业务范围的扩展**：例如，National Car Rental System, Inc.与EDS的外包合作，不仅涉及IT基础设施的运营管理，还包括共同开发和推广应用程序。

## A Diffusion-of-Innovation Perspective

### 扩散过程的基本要素

论文采用Rogers (1983)的扩散理论，认为创新扩散是通过社会系统中的某些渠道随时间传播的过程。扩散过程包括四个关键要素：

- **创新**：任何被认为新的想法、对象或实践。
- **社会系统**：潜在采用者的社区或组织。
- **沟通渠道**：信息在社会系统内或之间传递的手段。
- **时间**：社会系统成员采用创新的速度。

### IT外包的扩散视角

在IT外包的背景下，相关社会系统是那些利用IT基础设施实现其使命的组织集合。沟通渠道包括：

- **水平渠道**：社会系统成员之间的互动，如直接人际接触或间接观察。
- **垂直渠道**：社会系统成员与外部代理的互动，如供应商的促销努力、研究结果或其他国家的最佳实践总结。

## Influence Sources in the Diffusion of IT Outsourcing

### 内部影响

内部影响模型认为，扩散仅通过社会系统内的沟通渠道发生。扩散速率可以表示为：

$$
\frac{dN(t)}{dt} = qN(t)[m - N(t)]
$$

- **解释**：扩散速率取决于社区中已经采用创新的成员数量。潜在采用者通过模仿先前采用者来推动扩散。

### 外部影响

外部影响模型认为，扩散仅由社会系统外部的信息源驱动。扩散速率可以表示为：

$$
\frac{dN(t)}{dt} = p[m - N(t)]
$$

- **解释**：扩散速率仅取决于社会系统中潜在采用者的数量，而不考虑先前采用者与潜在采用者之间的互动。

### 混合影响

混合影响模型结合了内部和外部影响模型，可以表示为：

$$
\frac{dN(t)}{dt} = [p + qN(t)][m - N(t)]
$$

- **解释**：扩散速率由内部和外部影响的组合决定，适用于更复杂的情况。

## Kodak's Outsourcing as a Critical Event

### 关键事件的影响

论文将Kodak与IBM的外包合同视为一个关键事件，认为这一事件显著影响了IT外包的扩散模式。关键事件的定义和影响包括：

- **高可见性**：由于两家公司的显著性和合同金额（5亿美元），这一事件提高了IT外包的可见性。
- **管理者的评估**：这一事件促使其他组织的管理者认真评估外包作为一种治理选择。

### 研究问题

论文提出了两个主要研究问题：

1. **什么影响源最能描述IT外包的扩散？**
2. **在Kodak-IBM合同之前和之后，什么影响源最能描述IT外包的扩散？**

通过对这两个问题的探讨，论文旨在揭示IT外包扩散的内在机制和关键事件的影响。

---

### 第3章：Influence Sources in the Diffusion of IT Outsourcing

# 第3章：Influence Sources in the Diffusion of IT Outsourcing

## 理论视角

### IT外包作为行政创新

- **定义与背景**：
  - IT外包被定义为外部供应商显著贡献用户组织IT基础设施的物理和/或人力资源。这一定义与研究者和实践者近期关于IT外包的讨论一致。
  - IT外包被视为一种行政创新，因为它代表了组织治理模式的根本转变，从传统的层级控制和与供应商的市场交易转向混合模式或伙伴关系。

- **行政创新的特征**：
  - **治理模式的转变**：从传统的层级控制转向混合模式或伙伴关系，这种转变涉及战略和操作机制的重大变革。
  - **内部流程的变化**：IT外包导致用户组织内部业务流程和角色的重大调整，例如，信息系统执行官需要学习新的技能以监督外包合同。
  - **外部环境的变化**：从与IT市场的“疏远”关系转向“准企业”模式，强调与少数选定合作伙伴的长期联盟。

### 扩散创新视角

- **扩散过程**：
  - 扩散是指创新通过社会系统中的某些渠道随时间传播的过程。扩散的四个关键要素包括创新、社会系统、沟通渠道和时间。

- **IT外包的扩散**：
  - IT外包的扩散涉及计算机使用组织，这些组织可能考虑采用IT外包。沟通渠道包括水平渠道（用户社区内的互动）和垂直渠道（与外部代理的互动，如供应商的促销努力）。

## IT外包扩散中的影响来源

### 内部影响

- **定义与模型**：
  - 内部影响视角认为扩散仅通过社会系统内的沟通渠道发生。扩散速率可以表示为：
    $$
    \frac{dN(t)}{dt} = qN(t)[m - N(t)]
$$
    其中，$N(t)$是时间$t$时的累计采纳数，$q$是内部影响系数，$m$是系统的潜在采纳数。

- **实证支持**：
  - 内部影响模型在多个研究中得到了支持，例如Griliches（1957）对美国农民杂交种子玉米扩散的研究，以及Mansfield（1961）对工业创新扩散的研究。

### 外部影响

- **定义与模型**：
  - 外部影响视角认为扩散仅由社会系统外部的信息源驱动。扩散速率可以表示为：
    $$
    \frac{dN(t)}{dt} = p[m - N(t)]
$$
    其中，$p$是外部影响系数。

- **实证支持**：
  - 外部影响模型在某些研究中得到了支持，例如Rogers和Kincaid（1981）对BITNET扩散的研究。

### 混合影响

- **定义与模型**：
  - 混合影响视角结合了内部和外部影响模型，可以表示为：
    $$
    \frac{dN(t)}{dt} = [p + qN(t)][m - N(t)]
$$

- **实证支持**：
  - 混合影响模型在多个研究中得到了支持，例如Bass（1969）对电器产品扩散的研究。

## 研究问题

### 研究问题1：IT外包扩散的最佳影响来源是什么？

- **方法**：
  - 使用非线性最小二乘法（NLS）估计三种模型，并使用Davidson和MacKinnon（1981）开发的模型规范测试来比较模型。

- **结果**：
  - 内部影响模型在绝对拟合度上表现最佳，且在P检验中显著优于外部影响模型。

### 研究问题2：Kodak的IT外包决策作为关键事件，如何影响扩散模式？

- **方法**：
  - 将时间分为两个阶段：Kodak-IBM合同之前（1988年4月至1989年7月）和之后（1989年8月至1990年8月），分别评估内部和外部影响。

- **结果**：
  - 在Kodak-IBM合同之后的阶段，内部影响模型显著优于外部影响模型，表明Kodak事件显著改变了IT外包扩散的影响来源。

## 结论

- **主要发现**：
  - 内部影响模型更好地解释了IT外包的扩散模式，尤其是在Kodak-IBM合同之后的阶段。
  - Kodak事件作为一个关键事件，显著影响了IT外包扩散的模式，使得内部影响在合同之后变得更加显著。

- **理论贡献**：
  - 本研究通过实证分析支持了IT外包作为一种行政创新的观点，并提供了强有力的证据表明内部影响在IT外包扩散中的重要性。

- **方法论贡献**：
  - 本研究采用了更强大的非线性估计和规范测试方法，增强了扩散模型的分析能力。

- **未来研究方向**：
  - 未来的研究可以进一步探讨社会结构对IT外包扩散的影响，以及在不同经济环境中IT外包扩散的模式。

---

### 第4章：Kodak's Outsourcing as a Critical Event

# 第4章：Kodak's Outsourcing as a Critical Event

## Kodak与IBM的外包合同概述

Kodak与IBM在1989年7月宣布了一项独特的外包合同，该合同涉及将Kodak的数据中心运营转移给IBM。这一合同包括将四个数据中心和大约300名Kodak员工转移到IBM。这一事件在IT外包领域具有里程碑意义，因为它不仅涉及两家知名公司，而且合同金额高达5亿美元。

- **合同规模**：合同金额高达5亿美元，这在当时是一个巨大的数目，显示了Kodak对IT外包的重视和投入。
- **员工转移**：约300名Kodak员工成为IBM的员工，这表明Kodak不仅转移了技术设施，还转移了大量的人力资源。
- **成本节约**：Kodak希望通过这一外包合同削减高达50%的运营成本，这显示了外包在成本节约方面的潜力。

## Kodak事件作为关键事件的合理性

Kodak与IBM的外包合同被视为一个关键事件（critical event），因为它在IT外包的扩散过程中起到了显著的推动作用。以下是几个支持这一观点的理由：

- **高可见性**：Kodak和IBM都是知名公司，它们的合作吸引了广泛的媒体关注和行业讨论。这种高可见性使得其他组织更容易注意到IT外包的潜力和优势。
- **合同规模和影响力**：合同金额巨大，涉及的员工数量众多，这使得这一事件在行业内产生了深远的影响。其他组织可能会因为Kodak的成功案例而重新评估自己的IT战略。
- **行业影响**：Kodak的决策被视为一个信号，表明即使是大型和知名的公司也可以通过外包来提高效率和降低成本。这种示范效应可能会促使其他组织效仿。

## 理论支持：意见领袖和创新扩散

根据Rogers和Kincaid的研究，意见领袖（opinion leader）的采用可以对后续的采用产生深远的影响。Kodak作为一个行业巨头，其决策可以被看作是意见领袖的行为，这种行为可以减少其他组织在采用IT外包时的不确定性。

- **信息获取**：意见领袖通常拥有多样化的信息来源，这使得他们的决策更具可信度。其他组织可以通过观察Kodak的成功经验来获取相关信息，从而减少自身的不确定性。
- **网络位置**：意见领袖通常在网络中处于中心位置，这使得他们的决策可以迅速传播到更广泛的受众。Kodak的决策通过媒体和行业网络的传播，可能会迅速影响到其他组织。

## 研究设计与数据分析

为了验证Kodak事件对IT外包扩散的影响，研究者将时间分为两个阶段：Kodak事件之前（pre-Kodak）和之后（post-Kodak）。通过比较这两个阶段的扩散模式，研究者可以评估Kodak事件的影响。

- **数据收集**：研究者通过电子搜索两个CD-ROM数据库，收集了1988年4月至1990年8月期间的60个外包合同。
- **模型分析**：使用非线性最小二乘法（NLS）估计三种影响模型（内部影响、外部影响和混合影响），并通过J检验和P检验比较这些模型与白噪声模型的拟合度。

## 研究结果

研究结果表明，在Kodak事件之后，内部影响成为IT外包扩散的主要驱动力，而在Kodak事件之前，没有单一的影响源占据主导地位。

- **内部影响的主导地位**：在post-Kodak阶段，内部影响模型在统计上显著优于外部影响模型，这表明组织在决定是否采用IT外包时，更多地受到其他已经采用外包的组织的示范效应的影响。
- **Kodak事件的显著影响**：Kodak事件显著改变了IT外包扩散的模式，使得内部影响成为主要的驱动力。这与DiMaggio和Powell提出的制度同构理论（institutional isomorphism）相符，即组织在面对不确定性时，倾向于模仿其他成功组织的做法。

## 结论

Kodak与IBM的外包合同被视为IT外包扩散过程中的一个关键事件，因为它显著改变了组织在决定是否采用IT外包时的决策模式。通过这一事件，内部影响成为IT外包扩散的主要驱动力，这表明组织在面对不确定性时，更倾向于模仿其他成功组织的做法。这一发现不仅为IT外包的扩散提供了新的视角，也为理解创新扩散过程中的关键事件提供了重要的实证支持。

---

### 第5章：Methods

# 第5章：Methods

本文在第5章中详细介绍了研究方法，包括样本选择、分析框架以及模型测试方法。以下是对这一章节的详细分析。

## 样本选择

- **数据来源**：作者通过电子搜索两个CD-ROM数据库——University Microfilms International (UMI) 的 Newspaper Abstracts Ondisc 和 Business Dateline Ondisc ——来生成外包合同样本。这两个数据库分别包含七家主要日报的索引和近180种区域杂志、日报及新闻服务的完整文章文本。
- **搜索时间范围**：搜索从1985年1月开始（这是数据库的起始日期），并确定了1988年4月至1990年8月期间的60个外包合同。
- **合同类型**：样本包括设施管理外包和系统集成外包，这两种是目前实践中最主要的IT外包模式，并且与作者的定义一致。
- **合同特征**：平均合同金额为1.911亿美元，标准差为5.899亿美元；平均合同期限为5.6年，标准差为5.0年。

## 分析框架

- **模型选择**：作者采用了Mahajan等人(1988)开发的非线性最小二乘法(NLS)估计方法，而不是线性类比方法，以克服多重共线性和无法获得关键参数（p, q, m）的标准误差等计量经济学限制。
- **模型测试**：使用了Davidson和MacKinnon(1981)开发的特殊模型规范测试类，包括J-test和P-test，以测试线性零假设和非线性替代假设。

### 零假设

- **描述**：零假设是一个严格的白噪声或随机游走过程，即采用者在时间t和时间(t-1)之间的差异是随机的，扩散速率仅由误差项驱动。
- **数学形式**：x(t) = x(t - 1) + e(t)，其中e(t)是均值为零且与所有e(t-k)无关的误差项。

### 内部影响模型

- **数学形式**：dN(t)/dt = qN(t)[m - N(t)]，其中N(t)是时间t的累计采用数量，q是内部影响系数，m是系统的潜在采用数量。
- **离散形式**：x(t) = m / [1 + ((m - m0) / m0)exp(-qmt)]，其中m0是初始时期的采用者数量。

### 外部影响模型

- **数学形式**：dN(t)/dt = p[m - N(t)]，其中p是外部影响系数。
- **离散形式**：x(t) = m[exp(-p(t - 1)) - exp(-pt)]。

### 混合影响模型

- **数学形式**：dN(t)/dt = [p + qN(t)][m - N(t)]。
- **离散形式**：x(t) = m / [1 + (q/p)exp(-(p + q)t)] * [1 + (q/p)exp(-(p + q)(t - 1))]。

## 模型测试方法

- **J-test**：用于测试线性零假设和替代模型（内部、外部、混合影响模型）之间的差异。通过估计回归方程x(t) = (1 - a)f(t) + ag(t) + u(t)，其中f(t)是零假设的白噪声模型，g(t)是基于最大似然估计的替代模型，a是常数，u(t)是随机误差。
- **P-test**：用于测试非线性零假设和替代模型之间的差异。通过估计函数X(t) = (1 - a)f(t) + ag(t) + FB + u(t)，其中F是包含g(t)对B的导数的行向量。

## 研究问题评估

- **研究问题1**：通过在整个扩散期间对三种影响模型进行非线性估计，并使用J-test和P-test评估相对适用性，确定哪种影响模型最能描述IT外包的扩散模式。
- **研究问题2**：将时间分为两个阶段（前Kodak时期和后Kodak时期），在每个阶段分别应用相同的分析框架，评估Kodak-IBM合同是否显著改变了影响来源的模式。

## 结论

作者通过详细的样本选择和分析框架，结合强有力的计量经济学方法，验证了内部影响模型在描述IT外包扩散模式中的优越性，特别是在Kodak-IBM合同之后的时期。这一方法论不仅为理解IT外包的扩散提供了坚实的实证基础，也为未来的研究提供了方法论上的参考。

---

### 第6章：Results

# 第六章：Results

本文的第六章“Results”主要探讨了信息技术外包（IT Outsourcing）扩散过程中影响来源的分析结果。该章节通过实证研究，回答了两个主要的研究问题：1）IT外包扩散中的主要影响来源是什么？2）Kodak与IBM的外包合同是否作为一个关键事件，影响了IT外包扩散的模式？

## 研究问题1：IT外包扩散中的主要影响来源

### 内部影响模型与外部影响模型的比较

- **内部影响模型**：该模型假设扩散主要通过社会系统内部的沟通渠道进行，即潜在采用者通过观察和模仿已经采用创新的组织来进行决策。研究结果表明，内部影响模型在解释IT外包扩散方面具有最佳的绝对拟合度。
- **外部影响模型**：该模型认为扩散主要由社会系统外部的信息源驱动，例如供应商、咨询公司或行业出版物。相比之下，外部影响模型在解释力上不如内部影响模型。

### 模型比较的统计测试

- **J检验**：所有三种模型（内部影响、外部影响和混合影响）都显著优于白噪声模型（p < 0.01），这表明扩散过程并非随机。
- **P检验**：内部影响模型在与外部影响模型的比较中表现更优（p < 0.01），而混合影响模型则无法拒绝内部或外部影响模型。

### 结果的解释

- 研究结果表明，IT外包的扩散主要受到内部影响，即组织之间的模仿行为。这与Rogers（1983）和Mahajan等人（1988）的研究一致，表明当创新在社会系统中具有较高的可见性且不采用创新可能导致竞争劣势时，内部影响更为显著。

## 研究问题2：Kodak与IBM的外包合同作为关键事件的影响

### 预Kodak和后Kodak时期的分析

- **预Kodak时期**：在这一时期，所有三种模型在统计上都显著优于白噪声模型（p < 0.05），但P检验显示没有一种模型明显优于其他模型。
- **后Kodak时期**：在这一时期，所有三种模型在J检验中均显著（p < 0.05），但P检验显示内部影响模型在外部影响模型上表现更优（p < 0.05）。混合影响模型的结果与全时期分析相似。

### 结果的解释

- 研究结果表明，在Kodak与IBM的外包合同之后，内部影响在IT外包扩散中占据了主导地位。这表明Kodak的决策作为一个关键事件，显著改变了IT外包扩散的模式，使得组织更倾向于模仿已经采用外包策略的组织。

## 研究的稳健性检验

- **季度数据分析**：为了验证结果的稳健性，研究者还对季度数据进行了分析。结果显示，内部影响模型在季度数据上也表现出色，进一步支持了月度数据的结论。
- **设施管理外包的子样本分析**：由于Kodak的合同是设施管理外包，研究者还分析了仅包含设施管理外包合同的子样本。结果同样支持内部影响模型在后Kodak时期占主导地位的结论。

## 总结

通过对IT外包扩散过程中影响来源的实证分析，本文发现内部影响是IT外包扩散的主要驱动力，尤其是在Kodak与IBM的外包合同之后。这一发现不仅为IT外包的扩散提供了有力的解释，也为理解组织在面对复杂和不确定的技术环境时的决策行为提供了新的视角。

---

### 第7章：Discussion

# 第7章：Discussion

## 扩散机制与模仿行为

论文通过实证研究得出，IT外包的扩散主要受到内部影响（即模仿行为）的驱动，而不是外部影响。这一发现与Mahajan和Peterson（1985）以及Mahajan等人（1988）的研究结果一致，特别是在创新具有社会可见性且不采用该创新可能使组织处于竞争劣势的情况下。论文进一步指出，这种模仿行为在Kodak-IBM合同之后的时期更为显著，表明Kodak事件作为一个关键事件，显著影响了IT外包的扩散模式。

- **制度同构理论**：论文引用了DiMaggio和Powell（1983）的制度同构理论，解释了组织为何会模仿其他组织。制度同构现象源于组织必须考虑其他组织的主要因素。当组织技术复杂、目标模糊或环境导致象征性不确定性时，组织倾向于模仿其领域内被认为更合法或更成功的类似组织。
- **模仿作为组织惯例**：Nelson和Winter（1982）将模仿视为一种组织惯例，并指出嫉妒的公司会试图复制不完全观察到的成功。尽管特定行政创新的绩效效果从未完全为人所知，但模仿仍然可能发生，作为一种防止被排除在新资源或新竞争优势来源之外的保险措施。

## 理性与非理性行为

尽管模仿行为可能看起来是非理性的，但论文认为这并不一定是非理性的。从广义上讲，同构行为与DiMaggio和Powell（1983）所称的“集体理性”有关。Fulk等人（1990）在社会影响模型的背景下发展了理性概念，认为理性是主观的、回顾性的，并且归因于他人提供的信息的影响。

- **经济分析中的理性**：论文指出，企业可能基于合理且经济上合理的分析，将IT外包作为一种深思熟虑的战略选择。Loh和Venkatraman（1992b）的近期事件研究表明，市场对采用IT外包的公司产生了积极的市场价值影响，表明通过高效选择IT治理机制可以提升公司绩效，这与组织经济学（Williamson，1991）一致。

## 研究贡献与方法论

论文在多个方面对现有文献做出了贡献：

- **行政创新视角**：论文将IT外包视为一种行政创新，并提出了支持这一观点的论据。
- **方法论改进**：在方法论上，论文开发和使用了比传统方法更强大的分析估计技术。
- **实证评估**：从IT研究的角度来看，论文试图实证评估IT领域内一个重要且当前的行政创新的扩散模式，并补充了Gurbaxani（1990）在技术创新增长扩散领域的研究。

## 研究局限与未来方向

尽管论文提供了有力的实证支持，但也指出了研究的局限性，并提出了未来研究的方向：

- **合同规模**：研究结果基于足够大以值得在专业贸易期刊上报道的外包合同，可能对较小外包决策的可见性存在偏见。然而，作者认为较小的合同不太可能是模仿行为的关键催化剂。
- **时间范围**：研究的时间范围从1988年4月到1990年8月，可能受到媒体报道标准变化的影响。尽管如此，作者认为这一时间框架的选择是合理的，并且即使包括更早的外包决策，内部影响模型仍可能更为显著。
- **模型规格与估计**：论文指定了与概念分析水平一致的扩散模型，并采用了当前最先进的估计程序。未来的研究可以考虑使用动态扩散模型，以放宽恒定采用人口的假设。
- **社会结构的影响**：未来的研究可以扩展模型的范围，考虑社会结构对扩散的影响，例如通过事件历史方法纳入社会关系结构特征的影响。

## 结论

论文通过对IT外包扩散模式的实证研究，提供了强有力的证据支持内部影响模型，特别是在Kodak-IBM合同之后的时期。论文呼吁进一步研究IT外包，从横向和纵向的角度进行更全面的探讨。这一研究不仅丰富了IT外包领域的理论基础，还为未来的实证研究提供了方法论上的指导。

---

### 第8章：Limitations and Extensions

# 第8章：Limitations and Extensions

## 合同规模（Contract Size）

论文指出，其研究结果基于在专业贸易期刊上报道的足够大的外包合同，这可能对小型外包决策存在偏见。然而，作者认为由于小型合同的可见度较低，它们不太可能是IT外包扩散中模仿行为的关键催化剂。因此，排除小型合同不太可能对研究结果和解释产生不利影响。

- **潜在偏见**：尽管作者认为小型合同的影响较小，但这一假设需要进一步验证。小型合同可能在特定行业或组织中起到重要作用，尤其是在初期阶段或资源有限的组织中。
- **未来研究方向**：未来的研究可以包括更广泛范围的合同，包括那些未在专业期刊上报道的小型合同，以全面了解IT外包的扩散模式。

## 时间范围（Time-period）

论文的研究时间范围是从1985年1月到1990年8月，基于两个数据库的文献计量搜索。作者指出，在此期间之前没有发现符合其定义的IT外包案例。

- **数据覆盖限制**：尽管作者尽力涵盖所有相关数据，但贸易期刊可能在其报道IT外包合同时改变了标准和重点。此外，历史上许多组织从1960年代就开始外包IT操作，但这些合同通常不报道在媒体中。
- **历史背景**：作者提到，早期的外包形式主要限于时间共享或处理服务，涉及服务局、有限的集成计算机系统和应用软件，以及一些设施管理服务。这些形式的外包并不广泛，且通常与业务常规操作相关的运营协议有关。
- **未来研究方向**：未来的研究可以扩展时间范围，包括更早的外包案例，以更好地理解IT外包的演变和扩散模式。

## 模型规范和估计（Specification and Estimation of Models）

作者使用了一类与概念分析水平一致的扩散模型，并采用了当前最先进的估计程序。

- **模型改进**：尽管作者认为其模型规范相对于以往工作有所改进，但随着扩散视角在IT研究中的进展，研究者应评估理论论点是否可以在其他形式的扩散模型中框架化，例如动态扩散模型。
- **未来研究方向**：未来的研究可以探索更复杂的扩散模型，如动态扩散模型，以放松恒定采用人口的假设，并更好地捕捉IT外包扩散的动态特性。

## 模型范围的扩展（Expanding the Scope of the Model）

论文的模型假设了一个具有同质混合的人口，即任何一对先前的采用者和潜在采用者之间的通信可能性是相等的。

- **社会结构的影响**：从分析的角度来看，可以放宽这一假设，并使用事件历史方法将社会结构的影响纳入扩散建模中。
- **结构性特征的影响**：未来的研究可以探讨美国经济中IT外包扩散是否受到链接采用者的社会关系结构性特征的影响。例如，可以使用用户组织与技术供应商/系统集成商之间的连锁董事程度，以及共同专业机构和协会的成员资格作为变量。
- **理论支持**：这种方法可以基于Burt（1987）在医学创新扩散研究中考虑的凝聚力和结构等效性效应的理论支持。

## 补充IT外包的横截面模型（Complementing a Cross-sectional Model of IT Outsourcing）

论文的研究构成了一个纵向方法，用于解释IT外包的发生情况。

- **跨学科视角**：这种方法补充了Loh和Venkatraman（1992a）使用的横截面方法，后者从业务成本结构、业务绩效、财务杠杆、IT成本结构和IT绩效等企业层面因素实证检验了IT外包的水平。
- **综合模型**：未来的研究可以开发一个更全面的模型，结合社会系统层面的制度背景，以更全面地理解和验证IT外包的扩散模式。

## 结论

论文通过对IT外包扩散模式的研究，提供了强有力的实证支持，表明内部影响模型在Kodak-IBM外包合同之后比之前更为显著。作者呼吁进一步研究IT外包，无论是从横截面还是纵向的角度。

- **研究贡献**：论文在IT外包作为行政创新的扩散研究中做出了重要贡献，提出了新的理论论点，并采用了更强大的分析估计技术。
- **未来研究方向**：未来的研究应继续探索IT外包的扩散模式，结合更多的理论支持和实证证据，以全面理解这一重要的行政创新。

---

### 第9章：Conclusion

# 第9章：Conclusion

## 总结与主要发现

本文旨在探讨信息技术外包（IT Outsourcing）作为一种行政创新在美国经济中的扩散模式及其背后的影响因素。通过采用扩散模型，作者对1988年4月至1990年8月期间的60个外包合同样本进行了实证分析，得出了几个关键结论。

### 内部影响的主导地位

- **内部影响模型优于外部影响模型**：研究结果表明，IT外包的扩散更多地受到内部影响（即模仿行为）的驱动，而不是外部影响。这一发现与Mahajan和Peterson（1985）以及Mahajan等人（1988）的研究一致，表明当一项创新在社会系统中具有较高的可见性时，不采用该创新可能会使成员处于竞争劣势。
- **Kodak效应的显著影响**：将Kodak与IBM的外包合同作为一个关键事件，研究发现，在Kodak事件之后，内部影响变得更加显著。这表明，Kodak的决策对其他组织产生了强烈的示范效应，促使它们更倾向于模仿这一行为。

## 理论与实践意义

### 理论贡献

- **行政创新的视角**：本文将IT外包视为一种行政创新，并通过扩散模型的应用，为理解IT治理的变化提供了新的视角。这与Teece（1980）和Mahajan等人（1988）的定义相符，强调了组织在内部安排和外部协调方面的重大变化。
- **模仿行为的解释**：研究结果支持了模仿行为作为创新扩散的主要驱动力，尤其是在不确定性和复杂性较高的环境中。这与DiMaggio和Powell（1983）提出的制度同构理论相呼应，解释了组织为何倾向于模仿其他成功组织的做法。

### 实践意义

- **战略决策的参考**：对于企业管理者而言，了解IT外包扩散的主要驱动因素有助于制定更为有效的IT战略。特别是在面对不确定性和快速变化的技术环境时，模仿成功案例可以降低风险并提高竞争力。
- **市场趋势的预测**：研究结果表明，随着Kodak事件的示范效应，IT外包市场可能会继续增长，企业应密切关注行业动态，以便及时调整自身的IT治理策略。

## 研究局限与未来方向

尽管本研究提供了有力的证据支持内部影响的主导地位，但仍存在一些局限性，需要在未来的研究中加以改进。

### 合同规模的偏差

- **样本选择偏差**：研究仅包括那些足够大以至于被专业贸易期刊报道的外包合同，可能忽略了较小规模的决策。然而，作者认为这些较小的合同不太可能是模仿行为的主要催化剂，因此对整体结果的影响有限。

### 时间范围的限制

- **历史数据的缺失**：由于数据库的限制，研究未能涵盖1988年4月之前的外包案例。尽管如此，作者认为即使包括这些早期案例，内部影响模型仍可能更为显著。

### 模型假设的简化

- **同质混合假设**：模型假设社会系统中的个体之间具有相同的交流概率，未来研究可以考虑引入社会结构的影响，如使用事件历史方法来更准确地模拟扩散过程。

## 结论

综上所述，本文通过实证研究验证了IT外包作为一种行政创新的扩散模式，并强调了内部影响在这一过程中的主导作用。Kodak与IBM的外包合同作为一个关键事件，显著增强了内部影响的效应。未来的研究可以进一步探讨不同社会结构对扩散过程的影响，以及动态扩散模型在IT外包领域的应用。

---

## 📊 分析统计

- **综合分析**: ✅ 已完成
- **章节分析**: 9 个章节
- **总分析数**: 10 项
- **分析引擎**: 腾讯混元API
- **分析模式**: 多提示词深度分析

---

*本分析由腾讯混元API自动生成，采用多轮对话模式进行深度学术分析*
