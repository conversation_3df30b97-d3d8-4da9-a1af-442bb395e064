# 合同AI系统影响评估 - 数据收集模板

## 快速开始指南

### 第一步：立即可执行的分析
基于您现有的项目管理数据，您可以立即开始以下分析：

1. **使用提供的Python工具包**：
   ```bash
   python contract_ai_analyzer.py
   ```

2. **基础数据质量评估**：
   - 检查数据完整性（缺失值分析）
   - 计算现有绩效指标基线
   - 识别数据质量问题

3. **建立绩效基线**：
   - 项目周期基线
   - 成本控制基线
   - 客户满意度基线

### 第二步：补充关键数据收集
优先收集以下高价值数据：
- 合同AI使用记录（最重要）
- 风险预警记录
- 项目里程碑详细数据

### 第三步：开始对比分析
设定AI系统启用时间点，进行前后对比分析

## 1. 合同AI使用情况数据收集表

### 1.1 AI功能使用记录表
| 字段名称     | 数据类型     | 必填 | 说明               | 示例值                     |
| ------------ | ------------ | ---- | ------------------ | -------------------------- |
| 记录ID       | VARCHAR(50)  | 是   | 唯一标识           | AI_20240101_001            |
| 合同编号     | VARCHAR(50)  | 是   | 关联主合同         | CONTRACT_2024_001          |
| 项目编号     | VARCHAR(50)  | 是   | 关联项目           | 09xx23090252               |
| 使用时间     | DATETIME     | 是   | AI功能使用时间     | 2024-01-15 14:30:00        |
| 用户ID       | VARCHAR(50)  | 是   | 操作用户           | USER_001                   |
| AI功能模块   | VARCHAR(100) | 是   | 具体功能           | 风险识别/条款建议/合规检查 |
| 输入内容摘要 | TEXT         | 否   | 用户输入概要       | 技术服务合同条款审查       |
| AI建议内容   | TEXT         | 是   | AI生成的建议       | 建议增加知识产权保护条款   |
| AI置信度     | DECIMAL(3,2) | 是   | AI建议置信度       | 0.85                       |
| 用户操作     | VARCHAR(50)  | 是   | 用户最终操作       | 采纳/修改/拒绝             |
| 修改内容     | TEXT         | 否   | 用户修改说明       | 调整了违约责任条款         |
| 人工干预时间 | INT          | 否   | 人工处理时间(分钟) | 15                         |
| 最终决策     | TEXT         | 否   | 最终采用方案       | 采纳AI建议并增加补充条款   |

### 1.2 数据收集方法
```python
# 数据收集脚本示例
import pandas as pd
from datetime import datetime

def collect_ai_usage_data():
    """收集AI使用数据的模板函数"""
    
    # 从AI系统日志中提取数据
    ai_logs = extract_from_ai_system_logs()
    
    # 从用户操作记录中提取数据
    user_actions = extract_from_user_action_logs()
    
    # 数据合并和清洗
    combined_data = merge_and_clean_data(ai_logs, user_actions)
    
    return combined_data

def validate_ai_usage_data(df):
    """验证AI使用数据质量"""
    validation_results = {
        '数据完整性': check_completeness(df),
        '数据一致性': check_consistency(df),
        '数据准确性': check_accuracy(df)
    }
    return validation_results
```

## 2. 风险管理数据收集表

### 2.1 合同风险预警记录表
| 字段名称     | 数据类型      | 必填 | 说明             | 示例值                       |
| ------------ | ------------- | ---- | ---------------- | ---------------------------- |
| 预警ID       | VARCHAR(50)   | 是   | 唯一标识         | RISK_20240101_001            |
| 合同编号     | VARCHAR(50)   | 是   | 关联合同         | CONTRACT_2024_001            |
| 项目编号     | VARCHAR(50)   | 是   | 关联项目         | 09xx23090252                 |
| 预警时间     | DATETIME      | 是   | 风险预警时间     | 2024-01-15 09:00:00          |
| 风险类型     | VARCHAR(50)   | 是   | 风险分类         | 法律/财务/技术/商务/合规     |
| 风险子类型   | VARCHAR(100)  | 否   | 详细分类         | 付款条件/知识产权/技术可行性 |
| 风险等级     | VARCHAR(20)   | 是   | 风险级别         | 高/中/低                     |
| AI置信度     | DECIMAL(3,2)  | 是   | AI判断置信度     | 0.92                         |
| 预警描述     | TEXT          | 是   | 风险详细描述     | 付款周期过长，存在资金风险   |
| 影响评估     | TEXT          | 否   | 潜在影响分析     | 可能导致现金流紧张           |
| 建议措施     | TEXT          | 否   | AI建议的处理措施 | 建议缩短付款周期至30天       |
| 人工确认时间 | DATETIME      | 否   | 人工审核时间     | 2024-01-15 10:30:00          |
| 人工确认结果 | VARCHAR(20)   | 否   | 人工审核结论     | 确认/误报/部分确认           |
| 人工确认说明 | TEXT          | 否   | 审核说明         | 确认存在风险，但影响可控     |
| 处理措施     | TEXT          | 否   | 实际采取的措施   | 与客户协商调整付款条件       |
| 处理结果     | VARCHAR(50)   | 否   | 处理结果         | 已解决/部分解决/未解决       |
| 实际风险发生 | BOOLEAN       | 否   | 是否真实发生风险 | TRUE/FALSE                   |
| 实际损失金额 | DECIMAL(15,2) | 否   | 实际经济损失     | 50000.00                     |

### 2.2 合同纠纷记录表
| 字段名称     | 数据类型      | 必填 | 说明           | 示例值                     |
| ------------ | ------------- | ---- | -------------- | -------------------------- |
| 纠纷ID       | VARCHAR(50)   | 是   | 唯一标识       | DISPUTE_20240101_001       |
| 合同编号     | VARCHAR(50)   | 是   | 关联合同       | CONTRACT_2024_001          |
| 项目编号     | VARCHAR(50)   | 是   | 关联项目       | 09xx23090252               |
| 纠纷发生时间 | DATETIME      | 是   | 纠纷发生时间   | 2024-03-15 14:00:00        |
| 纠纷类型     | VARCHAR(100)  | 是   | 纠纷分类       | 付款纠纷/交付纠纷/质量纠纷 |
| 纠纷描述     | TEXT          | 是   | 纠纷详细情况   | 客户对交付质量不满意       |
| 争议金额     | DECIMAL(15,2) | 否   | 涉及金额       | 100000.00                  |
| 责任方       | VARCHAR(50)   | 否   | 主要责任方     | 我方/客户方/双方/第三方    |
| AI预警状态   | VARCHAR(50)   | 否   | 是否有AI预警   | 有预警/无预警/预警不准确   |
| 相关预警ID   | VARCHAR(50)   | 否   | 关联的预警记录 | RISK_20240101_001          |
| 解决方式     | VARCHAR(100)  | 否   | 纠纷解决方式   | 协商/调解/仲裁/诉讼        |
| 解决开始时间 | DATETIME      | 否   | 开始处理时间   | 2024-03-16 09:00:00        |
| 解决结束时间 | DATETIME      | 否   | 解决完成时间   | 2024-04-15 17:00:00        |
| 解决周期天数 | INT           | 否   | 解决用时       | 30                         |
| 解决结果     | VARCHAR(100)  | 否   | 最终结果       | 和解/败诉/胜诉/部分胜诉    |
| 经济损失     | DECIMAL(15,2) | 否   | 实际经济损失   | 25000.00                   |
| 法律费用     | DECIMAL(15,2) | 否   | 法律服务费用   | 8000.00                    |
| 其他费用     | DECIMAL(15,2) | 否   | 其他相关费用   | 2000.00                    |
| 经验教训     | TEXT          | 否   | 总结的经验教训 | 需要加强合同条款审查       |

## 3. 项目过程补充数据收集

### 3.1 项目里程碑详细记录表
| 字段名称       | 数据类型      | 必填 | 说明           | 示例值                     |
| -------------- | ------------- | ---- | -------------- | -------------------------- |
| 里程碑ID       | VARCHAR(50)   | 是   | 唯一标识       | MILESTONE_001              |
| 项目编号       | VARCHAR(50)   | 是   | 关联项目       | 09xx23090252               |
| 里程碑名称     | VARCHAR(100)  | 是   | 里程碑名称     | 需求确认/设计评审/测试完成 |
| 里程碑类型     | VARCHAR(50)   | 是   | 里程碑分类     | 关键节点/交付节点/审批节点 |
| 计划开始时间   | DATETIME      | 是   | 计划开始时间   | 2024-01-15 09:00:00        |
| 计划完成时间   | DATETIME      | 是   | 计划完成时间   | 2024-01-20 17:00:00        |
| 实际开始时间   | DATETIME      | 否   | 实际开始时间   | 2024-01-16 10:00:00        |
| 实际完成时间   | DATETIME      | 否   | 实际完成时间   | 2024-01-22 16:00:00        |
| 完成状态       | VARCHAR(20)   | 是   | 完成状态       | 按时完成/延期完成/未完成   |
| 延期天数       | INT           | 否   | 延期天数       | 2                          |
| 完成质量评分   | INT           | 否   | 质量评分(1-10) | 8                          |
| 延期原因       | TEXT          | 否   | 延期原因说明   | 客户需求变更               |
| 风险因素       | TEXT          | 否   | 影响因素       | 技术难度超预期             |
| AI建议采纳情况 | TEXT          | 否   | AI建议使用情况 | 采纳了进度管理建议         |
| 资源投入       | DECIMAL(8,2)  | 否   | 实际投入人天   | 15.5                       |
| 成本支出       | DECIMAL(15,2) | 否   | 实际成本支出   | 12000.00                   |

### 3.2 客户反馈补充记录表
| 字段名称   | 数据类型    | 必填 | 说明             | 示例值               |
| ---------- | ----------- | ---- | ---------------- | -------------------- |
| 反馈ID     | VARCHAR(50) | 是   | 唯一标识         | FEEDBACK_001         |
| 项目编号   | VARCHAR(50) | 是   | 关联项目         | 09xx23090252         |
| 反馈时间   | DATETIME    | 是   | 反馈时间         | 2024-02-15 14:30:00  |
| 反馈类型   | VARCHAR(50) | 是   | 反馈分类         | 表扬/建议/投诉/咨询  |
| 反馈渠道   | VARCHAR(50) | 否   | 反馈来源         | 电话/邮件/会议/系统  |
| 反馈内容   | TEXT        | 是   | 具体反馈内容     | 项目进度符合预期     |
| 满意度评分 | INT         | 否   | 客户满意度(1-10) | 9                    |
| 改进建议   | TEXT        | 否   | 客户改进建议     | 希望加强沟通频率     |
| 处理状态   | VARCHAR(20) | 否   | 处理状态         | 已处理/处理中/待处理 |
| 处理结果   | TEXT        | 否   | 处理结果说明     | 已调整沟通计划       |
| 影响评估   | VARCHAR(50) | 否   | 对项目的影响     | 正面/负面/中性       |

## 4. 数据收集实施计划

### 4.1 数据收集时间表
```
第1周：设计数据收集表单和系统接口
第2-3周：部署数据收集工具和培训相关人员
第4周开始：正式开始数据收集
每月：数据质量检查和清洗
每季度：数据分析和报告生成
```

### 4.2 数据质量保证措施
1. **数据验证规则**：
   - 必填字段检查
   - 数据格式验证
   - 逻辑一致性检查
   - 异常值识别

2. **数据收集培训**：
   - 培训相关人员正确填写数据
   - 建立数据收集标准操作程序
   - 定期进行数据质量审核

3. **自动化数据收集**：
   - 尽可能从系统日志自动提取数据
   - 减少人工录入错误
   - 建立数据同步机制

### 4.3 数据安全和隐私保护
1. **数据脱敏处理**：
   - 客户敏感信息脱敏
   - 个人信息匿名化
   - 商业机密信息保护

2. **访问权限控制**：
   - 分级访问权限设置
   - 数据使用审计日志
   - 定期权限审查

这个数据收集模板为您提供了：
- **标准化的数据收集表格**：确保数据一致性和完整性
- **详细的字段定义**：明确每个数据项的含义和要求
- **数据质量保证措施**：确保收集到高质量的分析数据
- **实施计划指导**：提供具体的执行步骤和时间安排

您希望我为哪个特定的数据收集环节提供更详细的实施指导？
