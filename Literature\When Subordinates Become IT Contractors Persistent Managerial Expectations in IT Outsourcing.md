# When Subordinates Become IT Contractors Persistent Managerial Expectations in IT Outsourcing

**分析时间**: 2025-07-19 00:40:05
**原文件**: pdf_paper\When Subordinates Become IT Contractors Persistent Managerial Expectations in IT Outsourcing.pdf
**文件ID**: file-pcAsT7Wx1pzyfFSjR39CIvor

---

## 📋 综合分析

# 一句话总结

这篇论文揭示了在IT外包中，尽管员工从下属变为外部承包商，客户经理仍会持续持有对他们的旧有期望，这种“持续的期望”会影响他们对承包商绩效的评估。

## 论文概览

### 研究背景和动机

自20世纪80年代末以来，信息系统外包的趋势持续不断。尽管已有大量研究关注公司为何外包（如Ang和Cummings 1997，Ang和Straub 1998等），但近期研究开始转向如何管理外包合同（如Ang和Beath 1993，Ang和Endeshaw 1997等）。外包的成功经验参差不齐，一些公司实现了预期目标，而另一些则面临管理变革的挑战。本文探讨了在IT外包中，客户经理对前下属（现为承包商）的持续期望现象。

### 主要研究问题

- 在IT外包中，客户经理为何会持续持有对前下属的旧有期望？
- 这种持续的期望对客户经理评估承包商绩效有何影响？

### 研究方法概述

本文采用混合方法，先通过定性研究（焦点小组）开发理论模型，再通过大规模定量调查验证模型。数据来自一个政府IT组织，该组织在前一年进行了IT外包。

### 核心贡献和创新点

- 提出了“持续的期望”这一概念，解释了客户经理在外包后仍对前下属持有旧有期望的现象。
- 通过混合方法，深入探讨了持续的期望的前因和后果，为IT外包管理提供了新的视角。

## 逐章详细分析

### Introduction

#### 章节主要内容

- 介绍了IT外包的背景和研究动机，指出了外包管理的挑战。
- 提出了研究问题：客户经理在外包后为何会持续持有对前下属的旧有期望，以及这种期望对承包商绩效评估的影响。

#### 关键概念和理论

- **IT外包**：公司将IT部门（包括员工、系统和操作）外包给外部实体。
- **持续的期望**：客户经理在外包后仍对前下属持有旧有期望。

#### 实验设计或分析方法

- 无具体实验设计，主要是引出研究问题和背景。

#### 主要发现和结论

- 外包后，客户经理可能会继续期望前下属按照以前的方式工作，尽管这些期望可能不再适用。

#### 与其他章节的逻辑关系

- 引出研究问题和背景，为后续章节的研究奠定基础。

### Persistent Expectations in IT Outsourcing

#### 章节主要内容

- 详细探讨了外包中客户经理与前下属关系的变化，从传统的上下级关系变为客户-承包商关系。
- 讨论了持续的期望现象及其前因和后果。

#### 关键概念和理论

- **代理理论**：在客户-承包商关系中，控制机制应从行为控制转向结果控制。
- **信念坚持**：人们倾向于坚持旧有的信念和期望，即使面对新的数据。

#### 实验设计或分析方法

- 无具体实验设计，主要是理论探讨。

#### 主要发现和结论

- 外包后，客户经理可能会继续期望前下属按照以前的方式工作，这种持续的期望会影响他们对承包商绩效的评估。

#### 与其他章节的逻辑关系

- 为后续的定性研究和定量研究提供了理论框架。

### Study 1: Development of Theoretical Model

#### 章节主要内容

- 通过焦点小组研究，探讨了持续的期望的前因和后果。
- 提出了理论模型，并通过定性数据分析验证了模型的初步构想。

#### 关键概念和理论

- **角色过载**：客户经理在外包后承担了更多的职责，导致角色过载。
- **关系强度**：客户经理与前下属的关系强度影响持续的期望。
- **信任**：客户经理对承包商的信任影响持续的期望。
- **外包经验**：客户经理的外包经验影响持续的期望。

#### 实验设计或分析方法

- 焦点小组研究，19名客户经理参与，讨论外包后的变化和持续的期望。

#### 主要发现和结论

- 角色过载、关系强度、信任和外包经验是持续的期望的前因，持续的期望影响客户经理对承包商绩效的评估。

#### 与其他章节的逻辑关系

- 为后续的定量研究提供了理论模型和假设。

### Study 2: Validation of Theoretical Model

#### 章节主要内容

- 通过问卷调查，验证了理论模型和假设。
- 使用PLS分析方法，检验了模型的测量有效性和结构模型参数。

#### 关键概念和理论

- **PLS分析**：一种结构方程模型分析方法，用于检验模型的测量有效性和结构模型参数。

#### 实验设计或分析方法

- 问卷调查，147名IT部门员工参与，测量了角色过载、关系强度、信任、外包经验、持续的期望和承包商绩效。

#### 主要发现和结论

- 角色过载和外包经验显著影响持续的期望，持续的期望显著影响客户经理对承包商绩效的评估。

#### 与其他章节的逻辑关系

- 验证了第一章提出的理论模型和假设，提供了实证支持。

## 总体评价

### 论文的优势和局限性

- **优势**：
  - 提出了“持续的期望”这一新概念，为IT外包管理提供了新的视角。
  - 采用混合方法，结合定性和定量研究，深入探讨了持续的期望的前因和后果。
  - 研究结果具有较强的理论和实践意义。

- **局限性**：
  - 研究数据来自单一组织，可能存在样本偏差。
  - 研究采用横截面数据，无法确定因果关系。

### 对相关领域的影响和意义

- 本文的研究为IT外包管理提供了新的理论框架和实践指导，特别是在如何管理客户经理的期望方面。
- 研究结果有助于理解外包中的人际关系变化及其对绩效评估的影响。

### 未来研究方向的建议

- 在不同类型的组织和外包合同中验证持续期望现象的普遍性。
- 采用纵向研究方法，进一步探讨持续的期望的动态变化及其长期影响。
- 探讨其他可能影响持续期望的因素，如合同管理技能和组织文化等。

---

## 📚 逐章节详细分析

以下是对论文各章节的深入分析，每个章节都经过了专门的AI分析：

### 第1章：Introduction

# 第1章：Introduction 详细分析

## 研究背景与趋势

### IT外包的持续增长
自20世纪80年代末以来，信息系统（IS）外包的趋势持续增长。这一趋势反映了企业为了提高效率、降低成本和专注于核心业务而将IT功能外包给外部供应商的普遍做法。外包不仅涉及技术资源的转移，还包括人员、流程和管理责任的重新分配。

### 研究焦点的转变
早期的研究主要集中在公司为何选择外包（如Ang和Cummings 1997，Ang和Straub 1998，Lacity和Hirschheim 1993等）。然而，近年来，研究的重点逐渐从外包的原因转向如何管理外包合同（如Ang和Beath 1993，Ang和Endeshaw 1997等）。这种转变反映了企业在实际操作中面临的管理挑战。

## 外包形式的探讨

### 旋转型外包（Spin-offs）
旋转型外包是一种常见的外包形式，特别是在大型组织中。通过这种方式，组织内的IT部门（包括员工、系统和运营）被“剥离”成一个独立的外部实体，成为外部供应商。这种形式的外包使IT员工正式离开原组织，转而被新成立的旋转型公司雇佣，并继续为原雇主提供服务。

- **优势**：旋转型外包的优势在于，转职员工（transplants）由于之前在原组织的经验，熟悉其运作和程序，减少了理解和适应新环境的时间和成本。此外，客户经理对转职员工的能力和技能有更深的了解，能够更有效地监督他们。

- **挑战**：尽管转职员工仍然为原组织提供服务，但他们不再直接受雇于原组织，而是由旋转型公司雇佣。这种身份的转变可能导致客户经理继续以旧有的下属关系来期望和管理这些员工，从而引发冲突。

## 持久管理期望的现象

### 社会心理学视角
从社会心理学的角度来看，期望和信念一旦形成，往往不容易改变，即使面对与之相矛盾的证据（如Anderson等人1980年的研究）。这种现象被称为“信念持久性”（belief perseverance），即在面对新的数据或证据时，先前的信念和期望仍然持续存在。

### 持久期望的定义
在本研究中，持久期望被定义为：即使在IT外包关系中，客户经理仍然期望转职员工像以前一样作为下属来履行职责和贡献，而不是根据新的合同关系来调整期望。这种期望可能导致客户经理继续对转职员工施加行为控制，而不是采用更适合外包关系的结果控制。

## 研究目标与方法

### 研究目标
本研究旨在探讨在IT旋转型外包安排中，客户经理对转职员工的态度和期望，特别是持久期望现象的发生条件及其对客户经理评估转职员工表现的影响。

### 研究方法
研究采用了混合方法，首先通过定性研究（案例研究）来发展理论模型，然后通过大规模的定量调查来验证该模型。这种方法允许研究者从多个角度理解和验证持久期望现象。

## 理论框架

### 代理理论
代理理论（Agency Theory）认为，在客户与承包商的关系中，控制机制应从主要基于行为的控制转变为主要基于结果的control。这是因为双方属于不同的组织，可能存在目标冲突。然而，客户经理可能仍然期望转职员工按照以前的行为标准来工作，而不是根据合同规定的结果标准。

### 认知心理学
认知心理学的研究表明，人们在面对新的信息时，往往会依赖已有的认知模式（schemas）来处理信息，而不是改变这些模式以适应新的情况。这种现象在IT外包的背景下，可能导致客户经理继续使用旧的认知模式来管理转职员工，即使这些模式不再适用。

## 结论
第1章为整篇论文奠定了基础，明确了研究背景、问题和方法。通过对IT外包趋势、旋转型外包的特点以及持久期望现象的探讨，研究提出了一个理论框架，旨在解释客户经理在IT外包中对转职员工的持久期望及其影响。这一框架为后续的实证研究提供了理论支持和研究方向。

---

### 第2章：Persistent Expectations in IT Outsourcing

# 第2章：Persistent Expectations in IT Outsourcing

## 引言

第2章主要探讨了在IT外包背景下，管理者对从前下属转变为合同工的人员持续存在的期望现象。这种现象被称为“persistent expectations”（持续期望），即在正式关系从上下级转变为合同关系后，管理者仍然按照以前的期望来对待合同工。本章通过社会心理学和代理理论的角度，深入分析了这一现象的成因、影响及其理论框架。

## 持续期望的理论基础

### 社会心理学视角

- **信念持续性理论**：该理论指出，即使面对新的数据或证据，人们仍然倾向于坚持原有的信念和期望。这一理论在IT外包背景下尤为重要，因为管理者可能难以改变对前下属的固有期望。
- **认知吝啬者理论**：人们倾向于使用已有的认知框架（schemas）来处理新信息，而不是改变这些框架以适应新的情况。这种认知偏差可能导致管理者在面对合同工时，仍然使用以前管理下属的方式。

### 代理理论视角

- **控制机制的变化**：代理理论认为，在代理关系从内部员工转变为外部合同工时，控制机制应从行为控制转向结果控制。然而，管理者可能仍然使用行为控制，因为他们对前下属有较高的期望。
- **合同的重要性**：在IT外包中，合同条款通常比雇佣合同更为具体和明确。这意味着合同工的工作范围和责任应严格按合同执行，但管理者可能仍然期望合同工履行以前作为下属时的职责。

## 持续期望的成因

### 角色过载

- **工作负担增加**：外包后，管理者需要承担更多的职责，如合同管理和供应商关系管理。这种角色过载可能导致管理者继续依赖前下属（现为合同工）来分担工作。
- **认知和动机偏差**：角色过载可能导致管理者在认知上难以适应新的工作环境，从而继续使用旧的期望和行为模式。

### 与前下属的强关系

- **社会关系的持续性**：管理者与前下属之间的强关系可能导致管理者难以将他们视为外部合同工。这种强关系可能包括频繁的互动和亲密的关系。
- **认知一致性**：强关系可能强化管理者对前下属的原有期望，使他们难以调整对这些人员的期望。

### 对合同工的信任

- **信任的影响**：管理者对合同工的信任可能影响他们对合同工的期望。高信任可能导致管理者期望合同工表现得更好，但这并不等同于期望他们履行以前作为下属时的职责。
- **信任与持续期望的关系**：尽管信任可能影响管理者的期望，但本章的研究发现信任与持续期望之间没有显著关系。

### 与第三方供应商的外包经验

- **经验的影响**：有与第三方供应商外包经验的管理者可能更容易将前下属视为外部合同工，因为他们已经建立了与外部供应商互动的期望和行为模式。
- **经验与持续期望的关系**：研究发现，有外包经验的管理者表现出较低的持续期望。

## 持续期望的影响

### 对合同工绩效评估的影响

- **绩效评估的偏差**：管理者对合同工的持续期望可能导致他们对合同工的绩效评估产生偏差。管理者可能基于更高的期望来评估合同工的表现，从而导致负面的评估结果。
- **情感反应的影响**：管理者对未满足期望的情感反应可能导致他们对合同工的绩效评估更加负面。

## 理论模型的构建

- **模型框架**：本章构建了一个理论模型，探讨了角色过载、与前下属的强关系、对合同工的信任和与第三方供应商的外包经验对持续期望的影响，以及持续期望对合同工绩效评估的影响。
- **假设的提出**：基于理论分析，提出了五个假设，分别探讨了上述因素与持续期望及其后果之间的关系。

## 结论

第2章通过社会心理学和代理理论的角度，深入分析了IT外包背景下管理者对前下属转变为合同工的持续期望现象。研究结果表明，角色过载、与前下属的强关系和与第三方供应商的外包经验是影响持续期望的重要因素，而持续期望又对合同工的绩效评估产生显著影响。这一研究为理解IT外包中的管理行为提供了新的视角，并为未来的研究提供了理论基础。

---

### 第3章：Study 1: Development of Theoretical Model

# 第3章：Study 1: Development of Theoretical Model

## 研究背景与目的

在第3章中，作者通过定性研究方法，即焦点小组讨论，来发展关于IT外包中“持续期望”现象的理论模型。研究的目的是探索在IT外包背景下，客户经理对前下属（现承包商）持续期望的形成条件及其影响。此研究基于社会心理学中的“信念坚持”理论和代理理论，旨在理解为何在正式关系从上下级转变为合同关系后，客户经理仍会继续对承包商施加与之前相同的行为期望。

## 研究方法

### 参与者与设置

- 研究对象为一家大型公共部门组织的员工，该组织向20个政府机构提供IT服务。在研究进行前八个月，该组织的系统建设和维护服务被剥离，成立了一个新的独立实体。
- 参与者包括19名客户经理，他们在焦点小组讨论中被随机选取，涵盖了不同的职位和任期。

### 研究程序

- 研究采用了三个焦点小组讨论，每个小组由6到7名客户经理组成，讨论持续约两小时。
- 讨论内容包括参与者在IT外包前后的工作变化、与承包商关系的变化、以及对承包商期望的变化。
- 讨论由第二作者主持，使用预先设计的提问协议来引导讨论。

### 数据分析

- 讨论内容由三位作者和两位研究助理独立记录，并使用NUDTST软件进行转录和分析。
- 采用Colaizzi（1978）的方法对转录内容进行编码和主题提取，最终确定了六个主要主题。
- 为了确保编码的可靠性，两位“盲”编码员使用相同的六个主题对转录内容进行独立编码，结果显示Cohen's Kappa值为0.92，表明编码具有高度一致性。

## 研究结果与理论模型

### 主要主题

1. **持续期望**：客户经理继续期望承包商像以前一样作为下属工作，尽管他们现在是外部承包商。
2. **角色过载**：客户经理在IT外包后经历了角色过载，导致他们对承包商的期望增加。
3. **与承包商的关系强度**：客户经理与前下属的紧密关系增加了持续期望的可能性。
4. **对承包商的信任**：客户经理对承包商的信任程度影响了他们对承包商的期望。
5. **与其他第三方供应商的外包经验**：有经验的客户经理更能将前下属视为外部承包商。
6. **客户经理对承包商绩效的评估**：持续期望影响了客户经理对承包商绩效的评估。

### 理论模型与假设

基于定性研究的结果，作者提出了一个理论模型，并提出了以下假设：

- **假设1**：角色过载与持续期望正相关。
- **假设2**：客户经理与承包商的关系强度与持续期望正相关。
- **假设3**：对承包商的信任与持续期望正相关。
- **假设4**：与其他第三方供应商的外包经验与持续期望负相关。
- **假设5**：持续期望与客户经理对承包商绩效的评估负相关。

## 讨论

在这一部分，作者详细讨论了每个主题的含义及其对持续期望现象的影响。例如，角色过载导致客户经理在面对外包带来的额外工作负担时，倾向于继续依赖前下属（现承包商）来完成任务，从而形成持续期望。此外，客户经理与前下属的紧密关系使得他们在心理上难以将前下属视为外部承包商，进一步加剧了持续期望的形成。

通过这一章的研究，作者为后续的定量研究奠定了理论基础，并提出了可验证的假设，为理解IT外包中的持续期望现象提供了深入的见解。

---

### 第4章：Study 1: Method

# Study 1: Method 详细分析

## 研究背景与目的

Study 1 的目的是通过定性研究方法，深入理解在IT外包背景下，客户经理对从前下属转变为合同工的持续期望现象。通过定性研究，研究者希望揭示这种现象的本质、影响因素及其后果，为后续的定量研究奠定理论基础。

## 研究设计与方法

### 参与者与设置

- **参与者**：研究参与者来自一个大型的公共部门组织，该组织为20个政府机构提供IT服务。在研究进行前八个月，该组织的系统建设和维护服务被剥离，成立了新的独立实体。
- **组织背景**：在剥离前，IT部门雇用了1501名员工，提供全方位的IT服务。其中约67%（1013名）是开发和维护人员，负责构建和维护应用程序软件及管理数据中心。其余488名员工是支持人员（如行政、财务和人力资源人员）以及IT规划者和解决方案提供者。
- **外包变化**：在剥离过程中，所有IT开发和维护人员正式从IT部门分离，被移植到新的独立实体。他们成为新组织的员工，但继续向前雇主（即“客户”组织）提供技术服务，作为合同工。

### 研究程序

- **焦点小组**：研究采用了焦点小组的方法，共进行了三组焦点小组讨论，每组6-7名客户经理参与，每组讨论平均持续两小时。参与者包括项目负责人和信息服务中心经理等，任期从三年到16年不等。
- **讨论内容**：讨论内容包括：
  - 外包前后他们的工作性质变化。
  - 他们在角色、职责、互动和与合同工关系方面的变化。
  - 对合同工期望和义务的变化。

### 数据收集与分析

- **数据收集**：讨论由第二作者主持，研究者开发了一个问题协议，指导参与者描述外包的影响，特别是与合同工（即从前下属转变为合同工）的关系。
- **数据分析**：讨论记录由两名作者和两名研究助理独立记录，并使用NUDTST软件进行转录和分析。采用Colaizzi（1978）的方法，独立阅读和编码转录笔记，发展出主要主题。通过两名“盲”编码员（MIS和管理学博士生）对转录笔记进行编码，确保编码的一致性和可靠性。

## 研究结果与理论模型

### 主要主题

- **持续期望**：客户经理继续期望合同工像以前一样作为下属工作。
- **角色过载**：客户经理因IT外包而承担了更多的职责，感到工作负担过重。
- **与合同工的关系强度**：客户经理与前下属的紧密关系影响了他们对合同工的管理和期望。
- **对合同工的信任**：客户经理对合同工的信任程度影响了他们的管理方式。
- **与其他第三方供应商的外包经验**：有经验的客户经理更能将合同工视为外部供应商。
- **对合同工绩效的评估**：持续期望影响了客户经理对合同工绩效的评估。

### 理论模型

基于定性研究的结果，研究者结合代理理论和社会认知理论，提出了一个关于持续期望的理论模型，并在后续的定量研究中进一步验证。

## 研究的贡献与局限

### 贡献

- **理论贡献**：通过定性研究方法，深入探讨了IT外包背景下客户经理对合同工的持续期望现象，提出了一个理论模型，为后续研究提供了基础。
- **方法论贡献**：采用混合方法（定性+定量）进行研究，确保了研究的全面性和深度。

### 局限

- **样本局限性**：研究仅在一个组织中进行，可能影响结果的普适性。
- **时间局限性**：数据收集在外包变化后八个月进行，可能无法完全反映长期影响。

## 总结

Study 1 通过定性研究方法，深入探讨了IT外包背景下客户经理对从前下属转变为合同工的持续期望现象。研究结果为后续的定量研究奠定了理论基础，并提出了一个关于持续期望的理论模型。尽管研究存在一定的局限性，但其贡献在于提供了对这一现象的深入理解，并为后续研究提供了重要的理论和方法论支持。

---

### 第5章：Study 1: Results and Theoretical Model

# Study 1: Results and Theoretical Model

## 研究方法与数据收集

在Study 1中，研究者采用了定性研究方法，通过焦点小组（focus groups）来探讨IT外包背景下管理者对前下属（现为合同工）的期望持续性问题。研究对象是一家大型公共部门组织的员工，该组织将其IT系统建设和维护服务外包给一个新的独立实体。研究选取了19名客户经理参与焦点小组讨论，这些经理在组织中担任不同的职位，如项目领导人和信息服务业经理，任期从三年到十六年不等。

## 主要研究发现

### 持续性期望（Persistent Expectations）

- 研究发现，客户经理在与前下属（现为合同工）的关系中，仍然期望他们像以前一样作为下属那样行事。这种期望持续存在的现象被称为“持续性期望”。
- 客户经理在与合同工的互动中，仍然期望合同工提供与以前相同的服务水平和承诺，尽管这些期望并未在新的外包合同中明确规定。

### 角色过载（Role Overload）

- 角色过载是导致持续性期望的一个重要因素。客户经理在外包后需要承担更多的职责和任务，而这些任务以前是由下属完成的。
- 由于外包导致的裁员，客户经理失去了下属的支持，导致他们在完成工作任务时感到不堪重负。这种角色过载使得客户经理更倾向于继续依赖前下属（现为合同工），并期望他们承担更多的责任。

### 与前下属的关系强度（Strength of Ties）

- 客户经理与前下属之间的紧密关系也对持续性期望产生了影响。那些与前下属关系密切的客户经理更难将前下属视为外部合同工，而是继续以同事的身份对待他们。
- 这种紧密关系使得客户经理在与合同工的互动中，仍然期望他们像以前一样提供支持和帮助。

### 对合同工的信任（Trust in Contractors）

- 研究还发现，客户经理对合同工的信任程度也会影响他们的期望。那些对合同工能力有较高信任的客户经理，更倾向于期望合同工能够履行更多的职责。
- 然而，这一因素在研究中并未显示出显著的统计关系，可能是因为信任并不一定转化为对合同工超出合同范围的期望。

### 外包经验（Outsourcing Experience）

- 有外包经验的客户经理更能够将前下属视为外部合同工，并且对他们的期望较低。这些经理已经建立了与第三方供应商互动的框架，能够更容易地调整他们的期望。
- 这一发现表明，先前的外包经验可以帮助客户经理更好地适应新的外包关系，减少对合同工的不切实际期望。

## 理论模型的构建

基于上述研究发现，研究者构建了一个理论模型来解释持续性期望的形成机制。该模型包括以下几个关键因素：

- **角色过载**：客户经理在外包后需要承担更多的职责，导致他们对合同工的期望持续存在。
- **与前下属的关系强度**：紧密的关系使得客户经理难以将前下属视为外部合同工，从而继续期望他们提供与以前相同的服务。
- **对合同工的信任**：虽然信任对期望有一定影响，但在本研究中并未显示出显著的统计关系。
- **外包经验**：有外包经验的客户经理能够更好地适应新的外包关系，减少对合同工的不切实际期望。

## 结论与意义

Study 1的结果表明，在IT外包背景下，客户经理对前下属的期望持续存在是一个普遍现象。这种持续性期望受到角色过载、与前下属的关系强度以及外包经验等因素的影响。研究结果为理解IT外包中的管理行为提供了重要的理论支持，并为未来的研究提供了基础。

通过这一研究，作者强调了在外包过程中，管理者需要调整他们的期望，以适应新的合同关系。这对于提高外包关系的效率和效果具有重要意义。

---

### 第6章：Study 2: Validation of Theoretical Model

# 第6章：Study 2: Validation of Theoretical Model

## 研究背景与目标

在第1阶段（定性研究）中，作者通过焦点小组讨论开发了一个关于“持续期望”（Persistent Expectations）的理论模型。第2阶段（定量研究）的目标是通过大规模的问卷调查来验证这一理论模型，特别是检验四个主要因素（角色过载、与承包商的关系强度、对承包商的信任、与第三方供应商的外包经验）对持续期望的影响，以及持续期望对承包商绩效评估的影响。

## 研究方法

### 参与者、设置和程序

- **参与者**：问卷调查对象为IT部门的幸存者，即在IT外包转换后仍在原组织的员工。共有147份有效问卷，回应率为89%。
- **程序**：问卷在10个月后的外包转换后发放，参与者在工作时间内完成问卷，确保了数据的及时性和相关性。

### 测量工具和预测试

- **测量工具**：问卷项目大多改编自先前研究中经过验证的测量工具，以确保其有效性和可靠性。
- **预测试**：在六名幸存者、两名IS经理和四名项目经理/领导中进行预测试，以确保项目的清晰度和内容效度。

### 持续期望的测量

- **测量项目**：包括七个项目，涉及客户经理对新承包商的一般期望。这些项目基于IS专业人士的工作价值观，并增加了一个关于承包商对技术问题贡献的项目。

### 其他变量的测量

- **角色过载**：改编自Peterson等人的研究，包含五个项目。
- **与承包商的关系强度**：基于Granovetter和Marsden & Campbell的研究，包含三个项目，分别衡量关系的强度、亲密程度和互动量。
- **对承包商的信任**：改编自Ganesan的研究，包含五个项目。
- **与第三方供应商的外包经验**：使用单一项目测量。
- **承包商绩效的感知**：基于SLA涵盖的活动，包含五个项目。

## 研究结果

### 工具有效性

- **内部一致性**：所有变量的Cronbach's α值均高于0.7，表明测量工具具有良好的内部一致性。
- **收敛效度和区分效度**：通过PLS分析，所有AVE（平均方差提取）值均大于0.5，且AVE值大于变量间的相关性，表明测量工具具有良好的收敛效度和区分效度。

### PLS结构模型测试

- **假设检验结果**：
  - **假设1**：角色过载与持续期望正相关（支持，t = 4.08, p < 0.01）。
  - **假设2**：与承包商的关系强度与持续期望正相关（支持，t = 1.48, p < 0.10）。
  - **假设3**：对承包商的信任与持续期望正相关（不支持，t = -0.53, p > 0.10）。
  - **假设4**：与第三方供应商的外包经验与持续期望负相关（支持，t = -2.21, p < 0.05）。
  - **假设5**：持续期望与承包商绩效评估负相关（不支持，t = 2.75, p < 0.05，实际为正相关）。

## 讨论

### 研究发现的解释

- **角色过载**：角色过载导致客户经理在面对新承包商时，继续依赖旧的期望模式，以应对工作压力。
- **关系强度**：与承包商的强关系使得客户经理难以调整对承包商的期望，继续保持将其视为下属的旧有思维。
- **外包经验**：有外包经验的客户经理更容易将前下属视为外部承包商，因为他们已经建立了与第三方供应商互动的期望模式。
- **持续期望与承包商绩效评估**：尽管假设认为持续期望会负向影响绩效评估，但结果却显示正向关系。这可能是由于社会认知理论中的“行为确认”现象，即客户经理的高期望导致承包商表现得更好，从而提高了绩效评估。

### 方法论解释

- **焦点小组讨论的偏差**：焦点小组讨论可能导致群体极化现象，使得初始观点被放大，从而影响了研究1的结果。
- **问卷调查的独立性**：问卷调查的独立性减少了社会影响，揭示了更真实的变量关系。

## 研究的局限性与未来研究方向

- **样本局限性**：研究仅在一个公共部门的IT部门进行，结果可能不具普遍性。
- **合同管理技能的影响**：尽管通过外包经验作为代理变量排除了合同管理技能的影响，但未来研究应直接测量合同管理技能以验证这一假设。
- **纵向研究**：由于数据是横截面的，无法确定变量间的因果关系，未来研究应采用纵向设计以更好地理解变量间的动态关系。

## 结论

第2阶段的研究通过定量方法验证了第1阶段开发的理论模型，提供了关于持续期望及其前因后果的实证证据。研究结果表明，角色过载、与承包商的关系强度和外包经验显著影响持续期望，而持续期望则正向影响承包商绩效评估。这些发现为IT外包管理提供了重要的理论和实践指导。

---

### 第7章：Study 2: Method

# Study 2: Method

## 参与者、设置和程序

### 参与者
- **样本来源**：问卷调查的对象是在IT部门工作的幸存者，这些员工在IT部门外包转换大约10个月后接受了调查。
- **样本数量**：在IT部门共有289名幸存者，其中19名参与了第一阶段的焦点小组讨论，因此被排除在第二阶段的研究之外，以避免启动效应和假设猜测。另外104名员工由于在外包后不与承包商互动，因此也不符合研究条件。最终，166名员工符合研究条件。
- **问卷发放和回收**：问卷在员工工作时间内，在各自的工作地点进行了半小时的会议发放。共收到147份完成的问卷，回应率为89%。

### 设置
- **参与者背景**：参与者平均年龄为30.9岁，平均工作年限为7.3年，其中在客户组织工作的平均年限为5.4年。性别分布为63名男性（43%）和84名女性（57%）。

### 程序
- **问卷发放**：问卷在各自的工作地点进行了半小时的会议发放，并当场收集。对于不在办公室的受访者，研究者要么亲自收集完成的问卷，要么让受访者直接邮寄回问卷。

## 测量和预测试

### 测量工具
- **问卷项目**：尽可能使用先前研究中经过验证的测量工具。除了受访者对第三方供应商的先前经验外，所有变量都测量了客户经理的感知。
- **预测试**：在六名幸存者、两名信息系统经理和四名项目经理/领导中进行预测试，以确保项目的清晰度和内容效度，并确保理论基础的项目能够触及客户经理关注的问题。根据预测试结果，进行了轻微修改。

## 客户经理的持久期望

### 测量方法
- **测量内容**：测量客户经理对新承包商的一般期望。选择了与IS专业人员工作性质相关的项目，并增加了一个项目来衡量承包商对技术问题的贡献。
- **信度**：Cronbach's α为0.95。

## 角色超载

### 测量方法
- **测量内容**：基于Peterson等人的研究，测量客户经理对角色超载的感知，包括五个项目。
- **信度**：Cronbach's α为0.86。

## 与承包商的关系强度

### 测量方法
- **测量内容**：基于先前研究，测量客户经理对新指定承包商的关系强度感知，分为三个维度：关系强度、亲密程度和互动量。
- **信度**：Cronbach's α为0.79。

## 对承包商的信任

### 测量方法
- **测量内容**：基于Ganesan的研究，测量客户经理对承包商的信任感知，包括五个陈述。
- **信度**：Cronbach's α为0.88。

## 与其他第三方供应商的外包经验

### 测量方法
- **测量内容**：使用单一项目测量客户经理与其他第三方供应商的外包经验。
- **理由**：因为这一评估是直接和事实性的，所以认为单一测量是足够的。

## 客户经理对承包商绩效的感知

### 测量方法
- **测量内容**：客户经理密切参与监控和评估承包商在SLA涵盖的所有活动上的表现。受访者被要求根据五个标准指出他们对新承包商表现的感知。
- **信度**：Cronbach's α为0.90。

## 研究结果

### 工具效度
- **PLS分析**：使用偏最小二乘法（PLS）分析测量效度和理论模型中的联系。PLS的优势在于它估计测量模型以确定构建的效度和可靠性，生成结构模型参数的估计值，并且不受其他结构方程建模工具的分布要求和样本大小限制。

### 内部一致性
- **PLS复合可靠性和Cronbach's α**：表2显示了PLS复合可靠性和Cronbach's α的结果，表明构建被可靠地测量，并且适合进行假设测试。

### 区分和收敛效度
- **AVE统计**：表1显示了所有AVE统计量在对角线上大于同一行和列中的交叉相关性，提供了构建区分性的证据，即构建与其自身比与其他构建更紧密地对齐。

### PLS结构模型测试
- **假设支持**：PLS用于测试假设。结果显示，角色超载与持久期望之间的关系得到了支持（f = 4.08, p < 0.01）。其他假设的结果也进行了讨论。

## 讨论

- **研究结果**：研究结果表明，尽管组织可能彻底改变了管理者和下属之间的工作关系，但基于先前关系的管理期望可能仍然存在。
- **影响因素**：角色超载、与承包商的关系强度和对承包商的信任等因素增加了持久期望的强度。
- **结果影响**：持久期望与对承包商绩效的感知显著正相关，这一结果与研究假设相反，但通过方法论解释和理论解释进行了讨论。

通过对Study 2: Method的详细分析，可以看出研究者在方法设计上进行了细致的考虑和严谨的实施，确保了研究的可靠性和有效性。

---

### 第8章：Study 2: Results

# Study 2: Results

## 仪器有效性（Instrument Validity）

在第二阶段的研究中，作者采用了偏最小二乘法（PLS）来分析测量模型的有效性和理论模型的结构参数。PLS方法具有几个优势，包括估计测量模型以确定构念的有效性和可靠性，以及通过潜在构念的指标来估计结构模型参数，从而测试假设关系的强度。

### 构造效度和信度

- **复合信度（Composite Reliability）** 和 **Cronbach's α** 被用来评估各个构造的信度。根据Nunnally (1967)的标准，这些结果表明构造被可靠地测量，并且适合进行假设测试。
- **平均方差提取（AVE）** 被用来评估区分效度和收敛效度。AVE的计算公式为 $\text{AVE} = \frac{\sum \lambda^2}{\sum \lambda^2 + \sum \theta}$，其中 $\lambda$ 是因子载荷，$\theta$ 是误差方差。表1显示，所有AVE统计量均大于对角线上的相关性，这表明构造具有区分效度。

### 构造的内部一致性

- 表2展示了构造的PLS复合信度和Cronbach's α。根据这些结果，所有构造都被可靠地测量，并且适合进行假设测试。

## PLS结构模型测试（PLS Structural Model Tests）

PLS也被用来测试假设。结果如图2和附录B所示。

### 假设支持情况

- **假设1**：角色过载与期望持续性之间的关系得到了支持（f = 4.08, p < 0.01）。
- **假设2**：与承包商的关系强度与期望持续性之间的关系在0.10水平上得到了支持，但在0.05水平上未得到支持（t = 1.48, p < 0.10）。
- **假设3**：对承包商的信任与期望持续性之间的关系不显著（f = -0.53, p > 0.10）。
- **假设4**：与第三方供应商的外包经验与期望持续性之间的关系在预测的方向上显著（f = -2.21, p < 0.05）。
- **假设5**：期望持续性与承包商绩效评估之间的关系与假设相反，显示出显著的正相关（t = 2.75, p < 0.05）。

### 解释方差

- 对于期望持续性，解释方差为17.5%。对于承包商绩效，解释方差较小，但作者认为这一结果仍然值得注意，因为即使在低解释方差的情况下，显著的关系也是值得追求的。

## 研究讨论（Study 2: Discussion）

### 持续性期望的影响因素

- **角色过载**：客户经理在角色过载的情况下，更倾向于依赖先前的模式来处理与承包商的关系，从而增加了期望持续性。
- **与承包商的关系强度**：与承包商关系密切的客户经理更难改变对承包商的看法，从而增加了期望持续性。
- **对外包经验的缺乏**：没有外包经验的客户经理更容易表现出期望持续性，而有经验的客户经理则能够更好地管理期望。

### 信任与期望持续性的关系

- 信任并未显著影响期望持续性。这可能是因为信任并不一定转化为对承包商超出合同要求的期望。

### 持续性期望对承包商绩效评估的影响

- 持续性期望与承包商绩效评估之间存在显著的正相关关系。这一结果与研究1的预期相反，但可以通过以下理论解释：
  - **感知受先前期望影响**：客户经理的先前期望可能影响了他们对承包商绩效的感知。
  - **行为确认**：客户经理的高期望可能导致承包商表现得更好，从而提高了绩效评估。

## 后验分析（Post Hoc Analyses）

### 合同管理技能的替代解释

- 作者通过数据分析排除了合同管理技能缺乏作为期望持续性的替代解释。数据显示，先前的外包经验与角色过载之间的相关性不显著，且外包经验与承包商绩效之间也没有显著关系。

## 结论

第二阶段的研究结果支持了大部分假设，尤其是角色过载和外包经验对期望持续性的影响。尽管信任与期望持续性之间的关系不显著，但这一结果并不影响整体研究的结论。研究还发现，期望持续性与承包商绩效评估之间存在正相关关系，这一结果需要进一步研究和验证。

通过这一研究，作者不仅验证了理论模型，还为IT外包中的管理期望提供了新的见解，强调了在合同关系变化后管理期望的重要性。

---

### 第9章：Study 2: Discussion

# 第9章：Study 2: Discussion

## 持久期望现象的验证与讨论

在第9章中，作者对持久期望（Persistent Expectations）现象进行了深入的讨论，涵盖了其前因后果以及相关假设的验证结果。这一章不仅总结了研究的主要发现，还探讨了这些发现的理论和实践意义。

### 主要研究发现

- **角色过载与持久期望的关系**：
  - 研究发现，角色过载（Role Overload）显著正向影响持久期望的存在。这意味着当客户经理面临更大的工作压力和职责时，他们更倾向于继续以过去的方式期望承包商，即期望承包商像以前的下属一样履行职责。
  - 这一发现与认知和动机偏差理论相符，即在角色过载的情况下，客户经理由于时间和认知资源的限制，更倾向于依赖已有的思维模式和期望，而不是根据新的合同关系调整他们的期望。

- **与承包商的关系强度**：
  - 与承包商的关系强度（Strength of Ties）也与持久期望呈正相关，尽管这一关系在统计上仅在0.10水平上显著。
  - 这表明，客户经理与承包商之间的紧密关系（如曾经的同事关系）会使得客户经理更难将承包商视为外部供应商，从而继续以过去的期望对待他们。

- **对承包商的信任**：
  - 对承包商的信任（Trust in Contractors）与持久期望之间的关系并不显著。这可能是因为信任虽然涉及对他人积极预期的信念，但并不一定转化为对承包商超出合同要求的期望。

- **第三方供应商的外包经验**：
  - 有第三方供应商外包经验的客户经理表现出较低的持久期望水平。这表明，拥有相关经验可以帮助客户经理更好地调整他们的期望，以适应新的合同关系。

- **持久期望对承包商绩效评估的影响**：
  - 持久期望与承包商绩效评估之间存在显著的正相关关系，这与研究假设相反。这一结果表明，客户经理的高期望可能导致他们对承包商的绩效评估更为严格，即使承包商实际上已经达到了合同规定的标准。

### 理论与实践意义

- **理论意义**：
  - 研究结果支持了持久期望在IT外包环境中的存在，并揭示了其前因后果。这为理解组织变革中的心理和行为动态提供了新的视角。
  - 研究还表明，尽管合同关系发生了变化，但客户经理的心理模式和期望可能不会立即改变，这对组织管理和变革管理提出了挑战。

- **实践意义**：
  - 组织可以通过提前通知、定期更新和多渠道沟通等方式，帮助客户经理调整他们的期望，以适应新的外包关系。
  - 提供合同管理培训和支持，可以帮助客户经理更好地管理外包关系，减少因持久期望带来的负面影响。

## 结论与未来研究方向

尽管研究提供了有力的证据支持持久期望的存在及其影响因素，但也存在一些局限性。例如，研究数据是横截面的，无法完全确定变量之间的因果关系。此外，研究仅在一个组织中进行，可能限制了结果的普遍性。

未来的研究可以探讨在不同类型的合同设计和管理环境下，持久期望的表现和影响。此外，研究可以进一步探讨合同管理技能对持久期望的影响，以验证替代解释的有效性。通过这些研究，可以更全面地理解IT外包中的心理和行为动态，为组织提供更有效的管理策略。

---

### 第10章：Conclusion

# Conclusion 章节详细分析

## 理论贡献

### 持久期望模型的提出
论文提出了一个关于IT外包中持久期望（persistent expectations）的理论模型。这一模型基于社会心理学中的"信念坚持"（belief perseverance）现象，解释了即使在正式关系从上下级转变为合同关系后，管理者仍可能继续以旧有的期望对待前下属（现为合同工）。这一理论贡献具有以下特点：

- **新颖性**：首次在IT外包背景下系统研究持久期望现象
- **理论整合**：结合了社会认知理论和代理理论
- **实践意义**：为理解外包关系中的管理行为提供了新视角

### 研究视角的创新
论文从管理者角度研究外包关系，这与以往更多关注承包商视角的研究形成对比：

- **管理可控性**：组织更容易影响自身管理者的期望而非前雇员的期望
- **态度改变机制**：提出了通过提前通知、重复信息、可信来源等多渠道改变管理者期望的方法

## 方法论贡献

### 混合研究方法的应用
论文采用质性研究与量化研究相结合的混合方法：

- **质性阶段**：通过焦点小组获取对持久期望现象的深入理解
  - *样本*：19名客户管理者参与3个焦点小组
  - *方法*：使用Colaizzi(1978)的编码程序确保编码可靠性(Cohen's Kappa=0.92)
  
- **量化阶段**：通过问卷调查验证理论模型
  - *样本*：147名IT部门幸存者(响应率89%)
  - *测量工具*：大部分量表基于已有研究改编，确保内容效度

这种混合方法设计弥补了单一方法的局限性：

- 质性研究提供丰富的现象描述和理论构建基础
- 量化研究验证理论的普遍性和统计显著性

## 实证发现

### 持久期望的前因变量
研究发现四个因素显著影响持久期望：

1. **角色超载**：
   - *假设1*：角色超载与持久期望正相关(支持，β=4.08,p<0.01)
   - *解释*：超负荷工作的管理者更依赖原有管理schema应对新关系

2. **与承包商的关系强度**：
   - *假设2*：关系强度与持久期望正相关(边际支持，p<0.10)
   - *机制*：强社会联系强化了原有的"组织内人员"schema

3. **对承包商的信任**：
   - *假设3*：信任与持久期望正相关(不支持)
   - *讨论*：信任不必然转化为对合同外行为的期望

4. **第三方外包经验**：
   - *假设4*：外包经验与持久期望负相关(支持，p<0.05)
   - *解释*：已有schema更易提取和应用

### 持久期望的结果变量
研究发现一个意外结果：

- *假设5*：持久期望与承包商绩效评价负相关(不支持)
- *实际发现*：显著正相关(p<0.05)
- *可能解释*：
  - 群体极化效应(焦点小组中负面评价被放大)
  - 信念坚持文献中的两种机制：
    * 感知偏差：管理者按高期望解读绩效
    * 行为确认：高期望引发承包商更好表现

## 研究局限与未来方向

### 主要局限性

1. **样本限制**：
   - 单一公共部门组织样本
   - 合同设计可能存在缺陷(未包含原下属执行的某些活动)

2. **时间因素**：
   - 横截面数据难以确定因果关系
   - 10个月后可能仍存在schema调整过程

3. **替代解释**：
   - 合同管理技能差异可能影响结果
   - 虽用外包经验作为代理变量，但直接测量更理想

### 未来研究建议

1. **跨组织比较**：
   - 比较设计良好与设计不良合同下的持久期望
   - 研究不同行业/外包目的的影响

2. **纵向研究**：
   - 追踪schema调整的动态过程
   - 考察时间对外包关系适应的影响

3. **承包商视角**：
   - 研究承包商是否也保持持久期望
   - 探讨双方期望匹配度对外包成功的影响

4. **合同管理能力**：
   - 开发直接测量合同管理技能的工具
   - 检验其对持久期望的调节作用

## 实践启示

论文为IT外包实践提供以下建议：

1. **管理者培训**：
   - 提前沟通关系变化
   - 提供合同管理技能培训
   - 建立新的绩效评估标准

2. **合同设计**：
   - 明确界定合同内外责任
   - 包含过渡期安排
   - 设立期望管理机制

3. **关系管理**：
   - 促进双方schema调整
   - 建立新的互动规范
   - 管理情感过渡过程

## 总结

这篇论文通过严谨的研究设计，揭示了IT外包中一个被忽视但重要的现象——持久期望。研究不仅丰富了代理理论和社会认知理论在外包领域的应用，也为组织管理外包关系提供了实践指导。尽管存在一些局限性，但其理论贡献和方法创新为后续研究奠定了坚实基础。

---

### 第11章：Limitations and Future Research

# 第11章：Limitations and Future Research 详细分析

## 研究的局限性

论文在第11章中详细讨论了其研究的局限性，这些局限性对于理解研究结果的有效性和普适性至关重要。

### 样本和组织的特定性

- **单一组织样本**：研究结果基于同一个客户组织的工作，这可能导致研究发现特定于该组织，而不能推广到其他组织。尽管该组织的IT员工在职责上与盈利性私营组织的员工相同，并且薪酬与市场水平相当，但组织文化、管理风格和其他未观察到的因素可能影响了研究结果。
- **合同设计问题**：研究中提到的外包合同设计不佳，未能包括之前由下属执行的一些活动，这可能引发了一些管理者对承包商服务水平下降的质疑。这种合同设计的缺陷可能是导致管理者感知服务水平下降的一个因素，而不一定是持久性期望的结果。

### 时间和因果关系

- **横向数据**：数据是横向收集的，即在某一时间点收集的数据，而不是纵向的。这使得研究者难以确定变量之间的因果关系。虽然研究设计试图通过混合方法来探讨持久性期望的前因和后果，但缺乏时间序列数据限制了对因果关系的明确推断。
- **时间限制**：研究在组织外包后10个月进行，管理者可能尚未成功改变其心理模式。这表明持久性期望可能在该研究中被特别强烈地体验，因为时间较短，管理者可能还没有完全适应新的关系。

### 管理者能力的替代测量

- **合同管理技能的替代测量**：研究中使用先前外包经验作为合同管理技能的代理变量。虽然这种方法提供了一种近似，但并不能精确捕捉合同管理技能。未来研究应包括具体的措施来准确捕捉这一变量，以测试合同管理技能对持久性期望的影响。

## 未来研究方向

论文提出了多个未来研究的方向，以进一步验证和扩展当前的研究发现。

### 合同设计的影响

- **比较不同合同设计下的持久性期望**：未来的研究应比较在良好设计和不良设计合同下持久性期望的持续性。这将有助于确定合同设计是否是影响持久性期望的重要因素，或者这种期望是否在任何合同设计下都会发生。

### 不同组织的普适性

- **跨组织和行业的比较**：研究应扩展到私营部门组织和具有不同外包目的的组织，以验证研究发现是否在不同背景下仍然成立。这将有助于确定持久性期望是否是一个普遍现象，还是特定于某些类型的组织或行业。

### 双边持久性期望

- **承包商的持久性期望**：虽然研究主要关注客户管理者的持久性期望，但外包关系是双向的，承包商也可能对客户管理者持有持久性期望。未来的研究可以探讨承包商是否也经历类似的持久性期望，以及这些期望如何影响他们的行为和表现。

### 长期影响和动态变化

- **长期跟踪研究**：采用纵向研究方法，跟踪管理者和承包商在外包关系中的变化，将有助于更好地理解持久性期望的动态变化及其长期影响。这将提供更有力的证据来支持或反驳当前研究的发现。

### 合同管理技能的具体测量

- **具体测量合同管理技能**：未来研究应开发和使用具体的措施来评估合同管理技能，以便更准确地测试其对持久性期望的影响。这将有助于更深入地理解管理者在外包关系中的角色和能力。

## 结论

尽管研究提供了关于IT外包中持久性期望的宝贵见解，但其局限性提醒我们在解释结果时需要谨慎。未来的研究应旨在解决这些局限性，并进一步探索持久性期望的形成机制和管理策略。通过这种方式，研究人员和组织可以更好地理解和应对外包关系中的挑战，从而提高外包的成功率和管理效率。

---

## 📊 分析统计

- **综合分析**: ✅ 已完成
- **章节分析**: 11 个章节
- **总分析数**: 12 项
- **分析引擎**: 腾讯混元API
- **分析模式**: 多提示词深度分析

---

*本分析由腾讯混元API自动生成，采用多轮对话模式进行深度学术分析*
